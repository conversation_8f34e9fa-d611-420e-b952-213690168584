import { GoogleCliError } from '../types';
import { Logger } from './Logger';
/**
 * Handles edge cases and validates inputs
 */
export declare class EdgeCaseHandler {
    private logger;
    constructor(logger: Logger);
    /**
     * Validate and sanitize search query
     */
    validateSearchQuery(query: string): string;
    /**
     * Sanitize search query
     */
    private sanitizeQuery;
    /**
     * Handle network connectivity issues
     */
    checkNetworkConnectivity(): Promise<boolean>;
    /**
     * Handle environment-specific issues
     */
    validateEnvironment(): void;
    /**
     * Check if required modules are available
     */
    private checkRequiredModules;
    /**
     * Check terminal capabilities
     */
    private checkTerminalCapabilities;
    /**
     * Handle API quota and rate limiting edge cases
     */
    handleQuotaExceeded(error: any): GoogleCliError;
    /**
     * Handle malformed API responses
     */
    validateApiResponse(response: any): void;
    /**
     * Handle timeout scenarios
     */
    createTimeoutError(timeout: number): GoogleCliError;
    /**
     * Handle SSL/TLS certificate issues
     */
    handleSSLError(error: any): GoogleCliError;
    /**
     * Handle proxy and firewall issues
     */
    handleProxyError(error: any): GoogleCliError;
    /**
     * Suggest recovery actions based on error type
     */
    suggestRecoveryActions(error: GoogleCliError): string[];
    /**
     * Check if error is recoverable with retry
     */
    isRecoverableError(error: GoogleCliError): boolean;
    /**
     * Calculate appropriate retry delay
     */
    calculateRetryDelay(attempt: number, error: GoogleCliError): number;
}
//# sourceMappingURL=EdgeCaseHandler.d.ts.map