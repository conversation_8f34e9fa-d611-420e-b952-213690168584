/**
 * Search result from Google Custom Search API
 */
export interface SearchResult {
    title: string;
    link: string;
    snippet: string;
    displayLink?: string;
    formattedUrl?: string;
}
/**
 * Google Custom Search API response
 */
export interface GoogleSearchResponse {
    kind: string;
    url: {
        type: string;
        template: string;
    };
    queries: {
        request: Array<{
            title: string;
            totalResults: string;
            searchTerms: string;
            count: number;
            startIndex: number;
            inputEncoding: string;
            outputEncoding: string;
            safe: string;
            cx: string;
        }>;
        nextPage?: Array<{
            title: string;
            totalResults: string;
            searchTerms: string;
            count: number;
            startIndex: number;
            inputEncoding: string;
            outputEncoding: string;
            safe: string;
            cx: string;
        }>;
    };
    context: {
        title: string;
    };
    searchInformation: {
        searchTime: number;
        formattedSearchTime: string;
        totalResults: string;
        formattedTotalResults: string;
    };
    items?: SearchResult[];
}
/**
 * Search options for the CLI
 */
export interface SearchOptions {
    query: string;
    count?: number;
    safe?: 'active' | 'off';
    language?: string;
    country?: string;
    format?: 'json' | 'table' | 'compact';
    noColor?: boolean;
    debug?: boolean;
}
/**
 * Configuration for the Google Custom Search API
 */
export interface GoogleSearchConfig {
    apiKey: string;
    searchEngineId: string;
    baseUrl?: string;
    timeout?: number;
    retries?: number;
}
/**
 * Error types for better error handling
 */
export declare enum ErrorType {
    NETWORK_ERROR = "NETWORK_ERROR",
    API_ERROR = "API_ERROR",
    RATE_LIMIT_ERROR = "RATE_LIMIT_ERROR",
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR",
    VALIDATION_ERROR = "VALIDATION_ERROR",
    UNKNOWN_ERROR = "UNKNOWN_ERROR"
}
/**
 * Custom error class for Google CLI
 */
export declare class GoogleCliError extends Error {
    readonly type: ErrorType;
    readonly statusCode?: number;
    readonly details?: unknown;
    constructor(message: string, type?: ErrorType, statusCode?: number, details?: unknown);
}
/**
 * Rate limiting configuration
 */
export interface RateLimitConfig {
    requestsPerSecond: number;
    requestsPerDay: number;
    burstLimit: number;
}
/**
 * Logger levels
 */
export declare enum LogLevel {
    ERROR = "error",
    WARN = "warn",
    INFO = "info",
    DEBUG = "debug"
}
/**
 * Terminal formatting options
 */
export interface FormatOptions {
    width?: number;
    maxSnippetLength?: number;
    showNumbers?: boolean;
    compact?: boolean;
    noColor?: boolean;
}
//# sourceMappingURL=index.d.ts.map