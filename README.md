# Google CLI

A fast, feature-rich command-line interface tool for performing Google web searches with beautiful terminal formatting. Get search results without opening a browser!

[![npm version](https://badge.fury.io/js/google-cli.svg)](https://badge.fury.io/js/google-cli)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D16.0.0-brightgreen.svg)](https://nodejs.org/)

## ✨ Features

- 🔍 **Fast Google Search**: Perform Google searches directly from your terminal
- 🎨 **Rich Formatting**: Beautiful, coloured output with proper text wrapping
- 📊 **Multiple Output Formats**: Table, compact, and JSON formats
- 🌍 **Localisation Support**: Search in different languages and countries
- 🛡️ **Safe Search**: Control safe search settings
- ⚡ **Rate Limiting**: Built-in rate limiting to respect API quotas
- 🔧 **Configurable**: Extensive configuration options
- 🧪 **Well Tested**: Comprehensive test suite with high coverage
- 📱 **Terminal Responsive**: Adapts to your terminal width

## 🚀 Quick Start

### Installation

```bash
# Install globally
npm install -g google-cli

# Or use npx (no installation required)
npx google-cli "your search query"
```

### Setup

1. **Get a Google API Key**:
   - Go to [Google Cloud Console](https://console.developers.google.com/)
   - Create a new project or select an existing one
   - Enable the Custom Search API
   - Create credentials (API key)

2. **Create a Custom Search Engine**:
   - Go to [Google Custom Search](https://cse.google.com/cse/)
   - Create a new search engine
   - Note down the Search Engine ID

3. **Set Environment Variables**:
   ```bash
   export GOOGLE_API_KEY="your-api-key-here"
   export GOOGLE_SEARCH_ENGINE_ID="your-search-engine-id-here"
   ```

   Or create a `.env` file:
   ```env
   GOOGLE_API_KEY=your-api-key-here
   GOOGLE_SEARCH_ENGINE_ID=your-search-engine-id-here
   ```

### Basic Usage

```bash
# Simple search
google-cli "TypeScript tutorial"

# Search with options
google-cli "Node.js best practices" --count 5 --format compact

# JSON output for scripting
google-cli "React hooks" --format json --no-color
```

## 📖 Usage

### Command Syntax

```bash
google-cli [options] <query>
```

### Options

| Option | Short | Description | Default |
|--------|-------|-------------|---------|
| `--count <number>` | `-n` | Number of results (1-10) | 10 |
| `--safe <level>` | `-s` | Safe search (active\|off) | active |
| `--language <code>` | `-l` | Language code (e.g., en, es, fr) | - |
| `--country <code>` | `-c` | Country code (e.g., us, uk, ca) | - |
| `--format <type>` | `-f` | Output format (table\|compact\|json) | table |
| `--no-color` | | Disable coloured output | false |
| `--debug` | `-d` | Enable debug mode | false |
| `--version` | `-v` | Show version | - |
| `--help` | `-h` | Show help | - |

### Examples

```bash
# Basic search
google-cli "machine learning tutorial"

# Limit results
google-cli "Python frameworks" --count 3

# Different output formats
google-cli "JavaScript tips" --format compact
google-cli "API documentation" --format json

# Language and region
google-cli "recettes de cuisine" --language fr --country fr

# Safe search off
google-cli "medical research" --safe off

# No colours (for scripts)
google-cli "server status" --no-color

# Debug mode
google-cli "troubleshooting" --debug
```

## 🎨 Output Formats

### Table Format (Default)
```
================================================================================
                                Search Results
================================================================================
[1] TypeScript: JavaScript With Syntax For Types
https://www.typescriptlang.org/
  TypeScript extends JavaScript by adding types to the language. TypeScript 
  speeds up your development experience by catching errors and providing...

[2] TypeScript Tutorial
https://www.w3schools.com/typescript/
  TypeScript is JavaScript with added syntax for types. This tutorial covers
  TypeScript basics, installation, and advanced features...
================================================================================
```

### Compact Format
```
1. TypeScript: JavaScript With Syntax For Types
https://www.typescriptlang.org/

2. TypeScript Tutorial  
https://www.w3schools.com/typescript/
```

### JSON Format
```json
[
  {
    "title": "TypeScript: JavaScript With Syntax For Types",
    "link": "https://www.typescriptlang.org/",
    "snippet": "TypeScript extends JavaScript by adding types...",
    "displayLink": "typescriptlang.org"
  }
]
```

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `GOOGLE_API_KEY` | Your Google API key | Yes |
| `GOOGLE_SEARCH_ENGINE_ID` | Your Custom Search Engine ID | Yes |
| `GOOGLE_API_BASE_URL` | Custom API base URL | No |
| `GOOGLE_API_TIMEOUT` | Request timeout in ms | No |
| `GOOGLE_API_RETRIES` | Number of retries | No |
| `NO_COLOR` | Disable colours globally | No |

### Rate Limiting

The tool includes built-in rate limiting to respect Google's API quotas:
- **Free Tier**: 100 requests per day
- **Per Second**: 10 requests per second
- **Burst Limit**: 5 requests per 10 seconds

## 🧪 Development

### Prerequisites

- Node.js 16.0.0 or higher
- npm or yarn

### Setup

```bash
# Clone the repository
git clone https://github.com/Sharma-IT/google-cli.git
cd google-cli

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your API credentials

# Build the project
npm run build

# Run tests
npm test

# Run with coverage
npm run test:coverage
```

### Scripts

```bash
npm run build          # Build TypeScript to JavaScript
npm run dev            # Run in development mode
npm run test           # Run tests
npm run test:watch     # Run tests in watch mode
npm run test:coverage  # Run tests with coverage
npm run lint           # Run ESLint
npm run lint:fix       # Fix ESLint issues
npm run format         # Format code with Prettier
```

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Shubham Sharma**
- GitHub: [@Sharma-IT](https://github.com/Sharma-IT)
- Email: <EMAIL>

## 🙏 Acknowledgments

- Google Custom Search API for providing search functionality
- The Node.js and TypeScript communities for excellent tooling
- All contributors who help improve this project

## 📊 Project Stats

- **Language**: TypeScript
- **Runtime**: Node.js
- **Test Coverage**: >80%
- **Dependencies**: Minimal and well-maintained
- **Bundle Size**: Optimised for CLI usage
