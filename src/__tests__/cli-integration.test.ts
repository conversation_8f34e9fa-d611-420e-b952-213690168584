import { spawn } from 'child_process';
import { join } from 'path';

describe('CLI Integration Tests', () => {
  const cliPath = join(__dirname, '../index.ts');
  const timeout = 30000; // 30 seconds

  beforeEach(() => {
    // Set test environment variables
    process.env.NODE_ENV = 'test';
    process.env.GOOGLE_API_KEY = 'test-api-key';
    process.env.GOOGLE_SEARCH_ENGINE_ID = 'test-search-engine-id';
  });

  describe('CLI Help', () => {
    it('should display help when no arguments provided', (done) => {
      const child = spawn('npx', ['ts-node', cliPath], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env }
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', () => {
        try {
          expect(stdout).toContain('google-cli');
          expect(stdout).toContain('Usage:');
          done();
        } catch (error) {
          done(error);
        }
      });

      child.on('error', (error) => {
        done(error);
      });

      setTimeout(() => {
        child.kill();
        done(new Error('Test timed out'));
      }, timeout);
    }, timeout);

    it('should display help with --help flag', (done) => {
      const child = spawn('npx', ['ts-node', cliPath, '--help'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env }
      });

      let stdout = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      child.on('close', (code) => {
        expect(stdout).toContain('google-cli');
        expect(stdout).toContain('Options:');
        expect(stdout).toContain('Examples:');
        done();
      });

      setTimeout(() => {
        child.kill();
        done();
      }, timeout);
    }, timeout);

    it('should display version with --version flag', (done) => {
      const child = spawn('npx', ['ts-node', cliPath, '--version'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env }
      });

      let stdout = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      child.on('close', (code) => {
        expect(stdout).toContain('1.0.0');
        done();
      });

      setTimeout(() => {
        child.kill();
        done();
      }, timeout);
    }, timeout);
  });

  describe('CLI Error Handling', () => {
    it('should handle missing API key gracefully', (done) => {
      delete process.env.GOOGLE_API_KEY;

      const child = spawn('npx', ['ts-node', cliPath, 'test query'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env }
      });

      let stderr = '';

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        expect(code).not.toBe(0);
        expect(stderr).toContain('Missing required configuration');
        done();
      });

      setTimeout(() => {
        child.kill();
        done();
      }, timeout);
    }, timeout);

    it('should handle invalid arguments gracefully', (done) => {
      const child = spawn('npx', ['ts-node', cliPath, 'test', '--count', 'invalid'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env }
      });

      let stderr = '';

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        // Should handle gracefully, not crash
        done();
      });

      setTimeout(() => {
        child.kill();
        done();
      }, timeout);
    }, timeout);
  });

  describe('CLI Options', () => {
    it('should accept debug flag', (done) => {
      const child = spawn('npx', ['ts-node', cliPath, 'test', '--debug'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env }
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        // Debug mode should be enabled
        done();
      });

      setTimeout(() => {
        child.kill();
        done();
      }, timeout);
    }, timeout);

    it('should accept no-color flag', (done) => {
      const child = spawn('npx', ['ts-node', cliPath, 'test', '--no-color'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env }
      });

      child.on('close', (code) => {
        // Should run without color
        done();
      });

      setTimeout(() => {
        child.kill();
        done();
      }, timeout);
    }, timeout);

    it('should accept count option', (done) => {
      const child = spawn('npx', ['ts-node', cliPath, 'test', '--count', '5'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env }
      });

      child.on('close', (code) => {
        // Should accept count option
        done();
      });

      setTimeout(() => {
        child.kill();
        done();
      }, timeout);
    }, timeout);

    it('should accept format option', (done) => {
      const child = spawn('npx', ['ts-node', cliPath, 'test', '--format', 'json'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env }
      });

      child.on('close', (code) => {
        // Should accept format option
        done();
      });

      setTimeout(() => {
        child.kill();
        done();
      }, timeout);
    }, timeout);
  });

  describe('Signal Handling', () => {
    it('should handle SIGINT gracefully', (done) => {
      const child = spawn('npx', ['ts-node', cliPath, 'test query'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env }
      });

      setTimeout(() => {
        child.kill('SIGINT');
      }, 1000);

      child.on('close', (code, signal) => {
        expect(signal).toBe('SIGINT');
        done();
      });

      setTimeout(() => {
        child.kill('SIGKILL');
        done();
      }, timeout);
    }, timeout);
  });
});
