import { Command } from 'commander';
import { SearchCommand } from '../SearchCommand';
import { Logger } from '../../utils/Logger';
import { <PERSON>rrorHandler } from '../../utils/ErrorHandler';
import { GoogleSearchService } from '../../services/GoogleSearchService';
import { ResultFormatter } from '../../utils/ResultFormatter';
import { ConfigValidator } from '../../utils/ConfigValidator';
import { GoogleCliError, ErrorType } from '../../types';
import { testUtils } from '../../test/setup';

// Mock dependencies
jest.mock('../../services/GoogleSearchService');
jest.mock('../../utils/ResultFormatter');
jest.mock('../../utils/ConfigValidator');

describe('SearchCommand', () => {
  let searchCommand: SearchCommand;
  let mockLogger: jest.Mocked<Logger>;
  let mockErrorHandler: jest.Mocked<ErrorHandler>;
  let mockSearchService: jest.Mocked<GoogleSearchService>;
  let mockFormatter: jest.Mocked<ResultFormatter>;
  let mockValidator: jest.Mocked<ConfigValidator>;
  let program: Command;

  beforeEach(() => {
    mockLogger = {
      error: jest.fn(),
      warn: jest.fn(),
      info: jest.fn(),
      debug: jest.fn(),
      success: jest.fn(),
    } as any;

    mockErrorHandler = {
      handleError: jest.fn(),
    } as any;

    mockSearchService = {
      search: jest.fn(),
    } as any;

    mockFormatter = {
      formatResults: jest.fn(),
    } as any;

    mockValidator = {
      validateConfig: jest.fn(),
    } as any;

    // Mock constructors
    (GoogleSearchService as jest.Mock).mockImplementation(() => mockSearchService);
    (ResultFormatter as jest.Mock).mockImplementation(() => mockFormatter);
    (ConfigValidator as jest.Mock).mockImplementation(() => mockValidator);

    searchCommand = new SearchCommand(mockLogger, mockErrorHandler);
    program = new Command();
  });

  describe('register', () => {
    it('should register command with program', () => {
      searchCommand.register(program);

      expect(program.commands).toHaveLength(1);
      const command = program.commands[0];
      expect(command.name()).toBe('google-cli');
    });

    it('should add help text with examples', () => {
      searchCommand.register(program);
      
      const helpText = program.helpInformation();
      expect(helpText).toContain('Examples:');
      expect(helpText).toContain('google-cli "TypeScript tutorial"');
    });
  });

  describe('execute', () => {
    beforeEach(() => {
      mockValidator.validateConfig.mockReturnValue({
        isValid: true,
        message: 'Configuration is valid'
      });

      mockSearchService.search.mockResolvedValue([
        testUtils.createMockSearchResult()
      ]);
    });

    it('should execute search successfully', async () => {
      const options = {
        count: '5',
        safe: 'active',
        format: 'table',
        noColor: false
      };

      // Access private method for testing
      const executeMethod = (searchCommand as any).execute.bind(searchCommand);
      await executeMethod('test query', options);

      expect(mockValidator.validateConfig).toHaveBeenCalled();
      expect(mockSearchService.search).toHaveBeenCalledWith({
        query: 'test query',
        count: 5,
        safe: 'active',
        format: 'table',
        noColor: false,
        debug: false
      });
      expect(mockFormatter.formatResults).toHaveBeenCalled();
      expect(mockLogger.success).toHaveBeenCalledWith('Found 1 result(s)');
    });

    it('should handle configuration validation failure', async () => {
      mockValidator.validateConfig.mockReturnValue({
        isValid: false,
        message: 'Missing API key'
      });

      const executeMethod = (searchCommand as any).execute.bind(searchCommand);
      
      await expect(executeMethod('test query', {})).rejects.toThrow();
      expect(mockErrorHandler.handleError).toHaveBeenCalled();
    });

    it('should handle no results', async () => {
      mockSearchService.search.mockResolvedValue([]);

      const executeMethod = (searchCommand as any).execute.bind(searchCommand);
      await executeMethod('test query', {});

      expect(mockLogger.warn).toHaveBeenCalledWith('No results found for your query.');
      expect(mockLogger.info).toHaveBeenCalledWith('Try using different keywords or removing filters.');
    });

    it('should handle search service error', async () => {
      const error = new GoogleCliError('API error', ErrorType.API_ERROR);
      mockSearchService.search.mockRejectedValue(error);

      const executeMethod = (searchCommand as any).execute.bind(searchCommand);
      
      await expect(executeMethod('test query', {})).rejects.toThrow();
      expect(mockErrorHandler.handleError).toHaveBeenCalledWith(error, 'Search');
    });
  });

  describe('parseOptions', () => {
    it('should parse options correctly', () => {
      const options = {
        count: '5',
        safe: 'off',
        language: 'en',
        country: 'us',
        format: 'json',
        noColor: true,
        debug: true
      };

      const parseOptionsMethod = (searchCommand as any).parseOptions.bind(searchCommand);
      const result = parseOptionsMethod('test query', options);

      expect(result).toEqual({
        query: 'test query',
        count: 5,
        safe: 'off',
        language: 'en',
        country: 'us',
        format: 'json',
        noColor: true,
        debug: true
      });
    });

    it('should handle invalid count', () => {
      const options = { count: 'invalid' };

      const parseOptionsMethod = (searchCommand as any).parseOptions.bind(searchCommand);
      const result = parseOptionsMethod('test query', options);

      expect(result.count).toBe(10); // Default value
    });

    it('should clamp count to valid range', () => {
      const parseOptionsMethod = (searchCommand as any).parseOptions.bind(searchCommand);
      
      const resultLow = parseOptionsMethod('test', { count: '0' });
      expect(resultLow.count).toBe(1);

      const resultHigh = parseOptionsMethod('test', { count: '20' });
      expect(resultHigh.count).toBe(10);
    });

    it('should trim query', () => {
      const parseOptionsMethod = (searchCommand as any).parseOptions.bind(searchCommand);
      const result = parseOptionsMethod('  test query  ', {});

      expect(result.query).toBe('test query');
    });

    it('should default safe search to active', () => {
      const parseOptionsMethod = (searchCommand as any).parseOptions.bind(searchCommand);
      const result = parseOptionsMethod('test', { safe: 'invalid' });

      expect(result.safe).toBe('active');
    });
  });

  describe('validateSearchOptions', () => {
    it('should validate valid options', () => {
      const options = {
        query: 'test query',
        count: 5,
        safe: 'active' as const,
        format: 'table' as const
      };

      const validateMethod = (searchCommand as any).validateSearchOptions.bind(searchCommand);
      expect(() => validateMethod(options)).not.toThrow();
    });

    it('should throw for empty query', () => {
      const options = { query: '' };

      const validateMethod = (searchCommand as any).validateSearchOptions.bind(searchCommand);
      expect(() => validateMethod(options)).toThrow(GoogleCliError);
    });

    it('should throw for query too long', () => {
      const options = { query: 'a'.repeat(2049) };

      const validateMethod = (searchCommand as any).validateSearchOptions.bind(searchCommand);
      expect(() => validateMethod(options)).toThrow(GoogleCliError);
    });

    it('should throw for invalid count', () => {
      const options = { query: 'test', count: 15 };

      const validateMethod = (searchCommand as any).validateSearchOptions.bind(searchCommand);
      expect(() => validateMethod(options)).toThrow(GoogleCliError);
    });

    it('should throw for invalid safe search', () => {
      const options = { query: 'test', safe: 'invalid' as any };

      const validateMethod = (searchCommand as any).validateSearchOptions.bind(searchCommand);
      expect(() => validateMethod(options)).toThrow(GoogleCliError);
    });

    it('should throw for invalid format', () => {
      const options = { query: 'test', format: 'invalid' as any };

      const validateMethod = (searchCommand as any).validateSearchOptions.bind(searchCommand);
      expect(() => validateMethod(options)).toThrow(GoogleCliError);
    });

    it('should warn for invalid language code', () => {
      const options = { query: 'test', language: 'invalid-lang' };

      const validateMethod = (searchCommand as any).validateSearchOptions.bind(searchCommand);
      validateMethod(options);

      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('Language code "invalid-lang" may not be valid')
      );
    });

    it('should warn for invalid country code', () => {
      const options = { query: 'test', country: 'invalid-country' };

      const validateMethod = (searchCommand as any).validateSearchOptions.bind(searchCommand);
      validateMethod(options);

      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('Country code "invalid-country" may not be valid')
      );
    });

    it('should accept valid language codes', () => {
      const validateMethod = (searchCommand as any).validateSearchOptions.bind(searchCommand);
      
      expect(() => validateMethod({ query: 'test', language: 'en' })).not.toThrow();
      expect(() => validateMethod({ query: 'test', language: 'en-US' })).not.toThrow();
    });

    it('should accept valid country codes', () => {
      const validateMethod = (searchCommand as any).validateSearchOptions.bind(searchCommand);
      
      expect(() => validateMethod({ query: 'test', country: 'us' })).not.toThrow();
      expect(() => validateMethod({ query: 'test', country: 'UK' })).not.toThrow();
    });
  });
});
