{"version": 3, "file": "GoogleSearchService.js", "sourceRoot": "", "sources": ["../../src/services/GoogleSearchService.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAAyD;AACzD,oCAOkB;AAGlB,8DAA2D;AAC3D,sDAAmD;AAEnD;;GAEG;AACH,MAAa,mBAAmB;IAK9B,YACU,MAAc,EACd,YAA0B;QAD1B,WAAM,GAAN,MAAM,CAAQ;QACd,iBAAY,GAAZ,YAAY,CAAc;QAElC,MAAM,SAAS,GAAG,IAAI,iCAAe,EAAE,CAAC;QACxC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;QACpC,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,CAAC;YACjC,iBAAiB,EAAE,EAAE,EAAE,6CAA6C;YACpE,cAAc,EAAE,GAAG,EAAI,wBAAwB;YAC/C,UAAU,EAAE,CAAC;SACd,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,MAAM,CAAC,OAAsB;QACxC,IAAI,CAAC;YACH,oBAAoB;YACpB,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC;YAEpC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;YAExE,0BAA0B;YAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAE/C,oCAAoC;YACpC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;YAE3D,2BAA2B;YAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEvD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,OAAO,CAAC,MAAM,UAAU,CAAC,CAAC;YACrD,OAAO,OAAO,CAAC;QAEjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,MAAM,MAAM,GAAG,eAAK,CAAC,MAAM,CAAC;YAC1B,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;YAC5B,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,KAAK;YACrC,OAAO,EAAE;gBACP,YAAY,EAAE,kBAAkB;gBAChC,QAAQ,EAAE,kBAAkB;gBAC5B,iBAAiB,EAAE,eAAe;aACnC;SACF,CAAC,CAAC;QAEH,kCAAkC;QAClC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAC7B,CAAC,MAAM,EAAE,EAAE;YACT,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE;gBACtC,GAAG,EAAE,MAAM,CAAC,GAAG;gBACf,MAAM,EAAE,MAAM,CAAC,MAAM;aACtB,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAChB,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;YACR,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC,CACF,CAAC;QAEF,mCAAmC;QACnC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC9B,CAAC,QAAQ,EAAE,EAAE;YACX,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACzC,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;aAChC,CAAC,CAAC;YACH,OAAO,QAAQ,CAAC;QAClB,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;YACR,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;gBAC9C,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM;gBAC9B,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,UAAU;gBACtC,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC,CAAC;YACH,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC,CACF,CAAC;QAEF,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,OAAsB;QAC9C,MAAM,MAAM,GAA2B;YACrC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;YACvB,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;YAC9B,CAAC,EAAE,OAAO,CAAC,KAAK;YAChB,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE;YACrC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,QAAQ;SAC/B,CAAC;QAEF,0BAA0B;QAC1B,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrB,MAAM,CAAC,EAAE,GAAG,QAAQ,OAAO,CAAC,QAAQ,EAAE,CAAC;QACzC,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,CAAC,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC;QAC9B,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAClC,MAA8B,EAC9B,aAAqB,CAAC;QAEtB,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,CAAC;QAE5C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAC3D,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,UAAU,GAAG,KAAmB,CAAC;YAEvC,2BAA2B;YAC3B,IAAI,UAAU,GAAG,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC5D,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;gBACnD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,KAAK,eAAe,UAAU,GAAG,CAAC,IAAI,UAAU,GAAG,CAAC,CAAC;gBAE9F,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACxB,OAAO,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;YAC7D,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,KAAiB;QACnC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACpB,mCAAmC;YACnC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;QAErC,yCAAyC;QACzC,OAAO,MAAM,IAAI,GAAG,IAAI,MAAM,KAAK,GAAG,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,UAAkB;QAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,CAAC,WAAW;QACnC,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,aAAa;QAErC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,QAAQ,CAAC,CAAC;QAEtE,wCAAwC;QACxC,OAAO,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,IAA0B;QACnD,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3C,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7B,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;YACjC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC;YACrC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,YAAY,EAAE,IAAI,CAAC,YAAY;SAChC,CAAC,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACK,SAAS,CAAC,IAAY;QAC5B,IAAI,CAAC,IAAI;YAAE,OAAO,EAAE,CAAC;QAErB,OAAO,IAAI;aACR,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAW,+BAA+B;aAC7D,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAU,2BAA2B;aACzD,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAO,wBAAwB;aACtD,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC;aACtB,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;aACrB,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;aACrB,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC;aACtB,IAAI,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,KAAc;QACtC,IAAI,KAAK,YAAY,sBAAc,EAAE,CAAC;YACpC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,eAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9B,MAAM,UAAU,GAAG,KAAmB,CAAC;YAEvC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACzB,gBAAgB;gBAChB,OAAO,IAAI,sBAAc,CACvB,gFAAgF,EAChF,iBAAS,CAAC,aAAa,EACvB,SAAS,EACT,UAAU,CACX,CAAC;YACJ,CAAC;YAED,MAAM,MAAM,GAAG,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC1C,MAAM,YAAY,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAW,CAAC;YAErD,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,GAAG;oBACN,OAAO,IAAI,sBAAc,CACvB,oBAAoB,YAAY,EAAE,KAAK,EAAE,OAAO,IAAI,aAAa,EAAE,EACnE,iBAAS,CAAC,gBAAgB,EAC1B,MAAM,EACN,YAAY,CACb,CAAC;gBAEJ,KAAK,GAAG,CAAC;gBACT,KAAK,GAAG;oBACN,MAAM,YAAY,GAAG,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC;wBAChD,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;oBAEpE,IAAI,YAAY,EAAE,CAAC;wBACjB,OAAO,IAAI,sBAAc,CACvB,qDAAqD,EACrD,iBAAS,CAAC,gBAAgB,EAC1B,MAAM,EACN,YAAY,CACb,CAAC;oBACJ,CAAC;oBAED,OAAO,IAAI,sBAAc,CACvB,wEAAwE,EACxE,iBAAS,CAAC,oBAAoB,EAC9B,MAAM,EACN,YAAY,CACb,CAAC;gBAEJ,KAAK,GAAG;oBACN,OAAO,IAAI,sBAAc,CACvB,iEAAiE,EACjE,iBAAS,CAAC,gBAAgB,EAC1B,MAAM,EACN,YAAY,CACb,CAAC;gBAEJ,KAAK,GAAG,CAAC;gBACT,KAAK,GAAG,CAAC;gBACT,KAAK,GAAG,CAAC;gBACT,KAAK,GAAG;oBACN,OAAO,IAAI,sBAAc,CACvB,uEAAuE,EACvE,iBAAS,CAAC,SAAS,EACnB,MAAM,EACN,YAAY,CACb,CAAC;gBAEJ;oBACE,OAAO,IAAI,sBAAc,CACvB,cAAc,YAAY,EAAE,KAAK,EAAE,OAAO,IAAI,eAAe,EAAE,EAC/D,iBAAS,CAAC,SAAS,EACnB,MAAM,EACN,YAAY,CACb,CAAC;YACN,CAAC;QACH,CAAC;QAED,gBAAgB;QAChB,MAAM,OAAO,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACvE,OAAO,IAAI,sBAAc,CACvB,kBAAkB,OAAO,EAAE,EAC3B,iBAAS,CAAC,aAAa,EACvB,SAAS,EACT,KAAK,CACN,CAAC;IACJ,CAAC;CACF;AAvTD,kDAuTC"}