{"version": 3, "file": "RateLimiter.js", "sourceRoot": "", "sources": ["../../src/utils/RateLimiter.ts"], "names": [], "mappings": ";;;AAAA,oCAAsE;AAEtE;;GAEG;AACH,MAAa,WAAW;IAKtB,YAAoB,MAAuB;QAAvB,WAAM,GAAN,MAAM,CAAiB;QAJnC,iBAAY,GAAa,EAAE,CAAC;QAC5B,sBAAiB,GAAW,CAAC,CAAC;QAC9B,kBAAa,GAAW,EAAE,CAAC;QAGjC,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,oBAAoB;QACpB,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YACzD,MAAM,IAAI,sBAAc,CACtB,0BAA0B,IAAI,CAAC,MAAM,CAAC,cAAc,uCAAuC,EAC3F,iBAAS,CAAC,gBAAgB,CAC3B,CAAC;QACJ,CAAC;QAED,yBAAyB;QACzB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;QAE3E,IAAI,cAAc,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;YAC3D,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,CAAC;YAClD,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,GAAG,GAAG,aAAa,CAAC,CAAC;YAE9C,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;gBACjB,MAAM,IAAI,sBAAc,CACtB,oCAAoC,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,2CAA2C,EACzG,iBAAS,CAAC,gBAAgB,CAC3B,CAAC;YACJ,CAAC;QACH,CAAC;QAED,oBAAoB;QACpB,MAAM,WAAW,GAAG,KAAK,CAAC,CAAC,aAAa;QACxC,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,GAAG,WAAW,CAAC,CAAC;QAEjF,IAAI,aAAa,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACnD,MAAM,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC;YACtD,MAAM,QAAQ,GAAG,WAAW,GAAG,CAAC,GAAG,GAAG,kBAAkB,CAAC,CAAC;YAE1D,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;gBACjB,MAAM,IAAI,sBAAc,CACtB,qCAAqC,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,2CAA2C,EAC1G,iBAAS,CAAC,gBAAgB,CAC3B,CAAC;YACJ,CAAC;QACH,CAAC;QAED,sBAAsB;QACtB,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,+BAA+B;QAE3D,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC,YAAY,EAAE,CAAC;QAExC,IAAI,IAAI,CAAC,aAAa,KAAK,KAAK,EAAE,CAAC;YACjC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;YAC3B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC7B,CAAC;IACH,CAAC;IAED;;OAEG;IACI,SAAS;QAKd,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,oBAAoB,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC;QACxF,MAAM,qBAAqB,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,MAAM,CAAC;QAE1F,OAAO;YACL,sBAAsB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC;YACxF,oBAAoB;YACpB,qBAAqB;SACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,WAAW;QAChB,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,oBAAoB;QACpB,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YACzD,MAAM,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;YAC5B,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YACzC,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9B,OAAO,QAAQ,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC;QAClC,CAAC;QAED,yBAAyB;QACzB,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;QAC3E,IAAI,cAAc,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;YAC3D,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,CAAC;YAClD,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,GAAG,GAAG,aAAa,CAAC,CAAC;YAC9C,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;gBACjB,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;QAED,oBAAoB;QACpB,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC;QAC3E,IAAI,aAAa,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACnD,MAAM,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC;YACtD,MAAM,QAAQ,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,kBAAkB,CAAC,CAAC;YACpD,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;gBACjB,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;QAED,OAAO,CAAC,CAAC;IACX,CAAC;IAED;;OAEG;IACI,KAAK;QACV,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;IAC1B,CAAC;CACF;AAjKD,kCAiKC"}