import { ConfigValidator } from '../ConfigValidator';

describe('ConfigValidator', () => {
  let validator: ConfigValidator;
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    validator = new ConfigValidator();
    originalEnv = { ...process.env };
    // Clear test environment variables to test actual validation logic
    delete process.env.NODE_ENV;
    delete process.env.GOOGLE_API_KEY;
    delete process.env.GOOGLE_SEARCH_ENGINE_ID;
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('validateConfig', () => {
    it('should return valid when all required env vars are set', () => {
      process.env.GOOGLE_API_KEY = 'AIzaSyDummyKeyForTesting123456789012345678';
      process.env.GOOGLE_SEARCH_ENGINE_ID = 'test-search-engine-id';

      const result = validator.validateConfig();

      expect(result.isValid).toBe(true);
      expect(result.message).toBe('Configuration is valid');
    });

    it('should return invalid when GOOGLE_API_KEY is missing', () => {
      delete process.env.GOOGLE_API_KEY;
      process.env.GOOGLE_SEARCH_ENGINE_ID = 'test-search-engine-id';

      const result = validator.validateConfig();

      expect(result.isValid).toBe(false);
      expect(result.missingFields).toContain('GOOGLE_API_KEY');
      expect(result.message).toContain('Missing required configuration');
    });

    it('should return invalid when GOOGLE_SEARCH_ENGINE_ID is missing', () => {
      process.env.GOOGLE_API_KEY = 'AIzaSyDummyKeyForTesting123456789012345678';
      delete process.env.GOOGLE_SEARCH_ENGINE_ID;

      const result = validator.validateConfig();

      expect(result.isValid).toBe(false);
      expect(result.missingFields).toContain('GOOGLE_SEARCH_ENGINE_ID');
    });

    it('should return invalid when both env vars are missing', () => {
      delete process.env.GOOGLE_API_KEY;
      delete process.env.GOOGLE_SEARCH_ENGINE_ID;

      const result = validator.validateConfig();

      expect(result.isValid).toBe(false);
      expect(result.missingFields).toEqual(['GOOGLE_API_KEY', 'GOOGLE_SEARCH_ENGINE_ID']);
    });

    it('should validate API key format', () => {
      process.env.GOOGLE_API_KEY = 'invalid-key';
      process.env.GOOGLE_SEARCH_ENGINE_ID = 'test-search-engine-id';

      const result = validator.validateConfig();

      expect(result.isValid).toBe(false);
      expect(result.message).toContain('invalid format');
    });

    it('should validate search engine ID format', () => {
      process.env.GOOGLE_API_KEY = 'AIzaSyDummyKeyForTesting123456789012345678';
      process.env.GOOGLE_SEARCH_ENGINE_ID = 'x';

      const result = validator.validateConfig();

      expect(result.isValid).toBe(false);
      expect(result.message).toContain('invalid format');
    });
  });

  describe('getConfig', () => {
    it('should return config with default values', () => {
      process.env.GOOGLE_API_KEY = 'test-api-key';
      process.env.GOOGLE_SEARCH_ENGINE_ID = 'test-search-engine-id';

      const config = validator.getConfig();

      expect(config.apiKey).toBe('test-api-key');
      expect(config.searchEngineId).toBe('test-search-engine-id');
      expect(config.baseUrl).toBe('https://www.googleapis.com/customsearch/v1');
      expect(config.timeout).toBe(10000);
      expect(config.retries).toBe(3);
    });

    it('should use custom environment variables', () => {
      process.env.GOOGLE_API_KEY = 'custom-api-key';
      process.env.GOOGLE_SEARCH_ENGINE_ID = 'custom-search-engine-id';
      process.env.GOOGLE_API_BASE_URL = 'https://custom.api.url';
      process.env.GOOGLE_API_TIMEOUT = '5000';
      process.env.GOOGLE_API_RETRIES = '5';

      const config = validator.getConfig();

      expect(config.apiKey).toBe('custom-api-key');
      expect(config.searchEngineId).toBe('custom-search-engine-id');
      expect(config.baseUrl).toBe('https://custom.api.url');
      expect(config.timeout).toBe(5000);
      expect(config.retries).toBe(5);
    });

    it('should handle invalid timeout and retries', () => {
      process.env.GOOGLE_API_TIMEOUT = 'invalid';
      process.env.GOOGLE_API_RETRIES = 'invalid';

      const config = validator.getConfig();

      expect(config.timeout).toBe(10000); // Default value
      expect(config.retries).toBe(3);     // Default value
    });
  });

  describe('validateNetworkConfig', () => {
    it('should return valid for default config', () => {
      const result = validator.validateNetworkConfig();

      expect(result.isValid).toBe(true);
      expect(result.message).toBe('Network configuration is valid');
    });

    it('should return invalid for timeout too low', () => {
      process.env.GOOGLE_API_TIMEOUT = '500';

      const result = validator.validateNetworkConfig();

      expect(result.isValid).toBe(false);
      expect(result.message).toContain('Timeout must be between');
    });

    it('should return invalid for timeout too high', () => {
      process.env.GOOGLE_API_TIMEOUT = '70000';

      const result = validator.validateNetworkConfig();

      expect(result.isValid).toBe(false);
      expect(result.message).toContain('Timeout must be between');
    });

    it('should return invalid for retries too low', () => {
      process.env.GOOGLE_API_RETRIES = '-1';

      const result = validator.validateNetworkConfig();

      expect(result.isValid).toBe(false);
      expect(result.message).toContain('Retries must be between');
    });

    it('should return invalid for retries too high', () => {
      process.env.GOOGLE_API_RETRIES = '15';

      const result = validator.validateNetworkConfig();

      expect(result.isValid).toBe(false);
      expect(result.message).toContain('Retries must be between');
    });
  });

  describe('isTestEnvironment', () => {
    it('should return true when NODE_ENV is test', () => {
      process.env.NODE_ENV = 'test';

      expect(validator.isTestEnvironment()).toBe(true);
    });

    it('should return true when GOOGLE_API_KEY is test key', () => {
      process.env.GOOGLE_API_KEY = 'test-api-key';

      expect(validator.isTestEnvironment()).toBe(true);
    });

    it('should return false in production environment', () => {
      process.env.NODE_ENV = 'production';
      process.env.GOOGLE_API_KEY = 'real-api-key';

      expect(validator.isTestEnvironment()).toBe(false);
    });
  });

  describe('private methods', () => {
    describe('isValidApiKeyFormat', () => {
      it('should accept valid API key format', () => {
        process.env.GOOGLE_API_KEY = 'AIzaSyDummyKeyForTesting123456789012345678';
        process.env.GOOGLE_SEARCH_ENGINE_ID = 'test-search-engine-id';

        const result = validator.validateConfig();
        expect(result.isValid).toBe(true);
      });

      it('should reject short API key', () => {
        process.env.GOOGLE_API_KEY = 'short';
        process.env.GOOGLE_SEARCH_ENGINE_ID = 'test-search-engine-id';

        const result = validator.validateConfig();
        expect(result.isValid).toBe(false);
      });

      it('should reject API key with invalid characters', () => {
        process.env.GOOGLE_API_KEY = 'AIzaSy@InvalidKey#123456789012345678';
        process.env.GOOGLE_SEARCH_ENGINE_ID = 'test-search-engine-id';

        const result = validator.validateConfig();
        expect(result.isValid).toBe(false);
      });
    });

    describe('isValidSearchEngineIdFormat', () => {
      it('should accept valid search engine ID', () => {
        process.env.GOOGLE_API_KEY = 'AIzaSyDummyKeyForTesting123456789012345678';
        process.env.GOOGLE_SEARCH_ENGINE_ID = 'valid-search-engine-id:123';

        const result = validator.validateConfig();
        expect(result.isValid).toBe(true);
      });

      it('should reject very short search engine ID', () => {
        process.env.GOOGLE_API_KEY = 'AIzaSyDummyKeyForTesting123456789012345678';
        process.env.GOOGLE_SEARCH_ENGINE_ID = 'x';

        const result = validator.validateConfig();
        expect(result.isValid).toBe(false);
      });
    });
  });

  describe('createMissingConfigMessage', () => {
    it('should create helpful message for missing API key', () => {
      delete process.env.GOOGLE_API_KEY;
      process.env.GOOGLE_SEARCH_ENGINE_ID = 'test-search-engine-id';

      const result = validator.validateConfig();

      expect(result.message).toContain('Get a Google API key');
      expect(result.message).toContain('console.developers.google.com');
      expect(result.message).toContain('export GOOGLE_API_KEY');
    });

    it('should create helpful message for missing search engine ID', () => {
      process.env.GOOGLE_API_KEY = 'AIzaSyDummyKeyForTesting123456789012345678';
      delete process.env.GOOGLE_SEARCH_ENGINE_ID;

      const result = validator.validateConfig();

      expect(result.message).toContain('Create a Custom Search Engine');
      expect(result.message).toContain('cse.google.com');
      expect(result.message).toContain('export GOOGLE_SEARCH_ENGINE_ID');
    });
  });
});
