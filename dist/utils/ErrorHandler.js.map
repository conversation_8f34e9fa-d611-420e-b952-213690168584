{"version": 3, "file": "ErrorHandler.js", "sourceRoot": "", "sources": ["../../src/utils/ErrorHandler.ts"], "names": [], "mappings": ";;;AACA,oCAAqD;AAGrD;;GAEG;AACH,MAAa,YAAY;IACvB,YAAoB,MAAc;QAAd,WAAM,GAAN,MAAM,CAAQ;IAAG,CAAC;IAEtC;;OAEG;IACI,WAAW,CAAC,KAA6B,EAAE,OAAgB;QAChE,MAAM,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QAErD,IAAI,KAAK,YAAY,sBAAc,EAAE,CAAC;YACpC,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;QAClD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,KAAqB,EAAE,aAAqB;QACvE,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,iBAAS,CAAC,aAAa;gBAC1B,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,GAAG,aAAa,kBAAkB,KAAK,CAAC,OAAO,EAAE,EACjD,KAAK,CACN,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;gBACzE,MAAM;YAER,KAAK,iBAAS,CAAC,SAAS;gBACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,GAAG,aAAa,qBAAqB,KAAK,CAAC,OAAO,EAAE,EACpD,KAAK,CACN,CAAC;gBACF,IAAI,KAAK,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;oBAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;oBAClF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;gBACpF,CAAC;gBACD,MAAM;YAER,KAAK,iBAAS,CAAC,gBAAgB;gBAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,GAAG,aAAa,wBAAwB,KAAK,CAAC,OAAO,EAAE,EACvD,KAAK,CACN,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;gBACxE,MAAM;YAER,KAAK,iBAAS,CAAC,oBAAoB;gBACjC,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,GAAG,aAAa,yBAAyB,KAAK,CAAC,OAAO,EAAE,EACxD,KAAK,CACN,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;gBAC9D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;gBACxE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;gBACjF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;gBAC7E,MAAM;YAER,KAAK,iBAAS,CAAC,gBAAgB;gBAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,GAAG,aAAa,qBAAqB,KAAK,CAAC,OAAO,EAAE,EACpD,KAAK,CACN,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;gBACvE,MAAM;YAER;gBACE,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,GAAG,aAAa,kBAAkB,KAAK,CAAC,OAAO,EAAE,EACjD,KAAK,CACN,CAAC;QACN,CAAC;QAED,wCAAwC;QACxC,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,KAAY,EAAE,aAAqB;QAC5D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,aAAa,GAAG,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;QAE7D,gDAAgD;QAChD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YAClF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;YACrE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;QAC3E,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QAC/D,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACpF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAED;;OAEG;IACI,WAAW,CAChB,OAAe,EACf,OAAkB,iBAAS,CAAC,aAAa,EACzC,aAAqB,EACrB,UAAmB;QAEnB,MAAM,KAAK,GAAG,IAAI,sBAAc,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;QAE3E,6CAA6C;QAC7C,IAAI,aAAa,IAAI,aAAa,CAAC,KAAK,EAAE,CAAC;YACzC,KAAK,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;QACpC,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,iBAAiB,CAC5B,EAAoB,EACpB,OAAgB;QAEhB,IAAI,CAAC;YACH,OAAO,MAAM,EAAE,EAAE,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,GAAG,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YACtE,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAC/B,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,KAA6B;QACrD,IAAI,KAAK,YAAY,sBAAc,EAAE,CAAC;YACpC,OAAO,KAAK,CAAC,IAAI,KAAK,iBAAS,CAAC,aAAa;gBACtC,KAAK,CAAC,IAAI,KAAK,iBAAS,CAAC,gBAAgB,CAAC;QACnD,CAAC;QAED,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;YACjC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC;YACnC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,sBAAsB,CAAC,KAA6B;QACzD,IAAI,KAAK,YAAY,sBAAc,EAAE,CAAC;YACpC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;gBACnB,KAAK,iBAAS,CAAC,aAAa;oBAC1B,OAAO,qEAAqE,CAAC;gBAC/E,KAAK,iBAAS,CAAC,SAAS;oBACtB,OAAO,oDAAoD,CAAC;gBAC9D,KAAK,iBAAS,CAAC,gBAAgB;oBAC7B,OAAO,wDAAwD,CAAC;gBAClE,KAAK,iBAAS,CAAC,oBAAoB;oBACjC,OAAO,2DAA2D,CAAC;gBACrE,KAAK,iBAAS,CAAC,gBAAgB;oBAC7B,OAAO,qDAAqD,CAAC;gBAC/D;oBACE,OAAO,+BAA+B,CAAC;YAC3C,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,OAAO,IAAI,4BAA4B,CAAC;IACvD,CAAC;CACF;AAzKD,oCAyKC"}