"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LogLevel = exports.GoogleCliError = exports.ErrorType = void 0;
/**
 * Error types for better error handling
 */
var ErrorType;
(function (ErrorType) {
    ErrorType["NETWORK_ERROR"] = "NETWORK_ERROR";
    ErrorType["API_ERROR"] = "API_ERROR";
    ErrorType["RATE_LIMIT_ERROR"] = "RATE_LIMIT_ERROR";
    ErrorType["AUTHENTICATION_ERROR"] = "AUTHENTICATION_ERROR";
    ErrorType["VALIDATION_ERROR"] = "VALIDATION_ERROR";
    ErrorType["UNKNOWN_ERROR"] = "UNKNOWN_ERROR";
})(ErrorType || (exports.ErrorType = ErrorType = {}));
/**
 * Custom error class for Google CLI
 */
class GoogleCliError extends Error {
    constructor(message, type = ErrorType.UNKNOWN_ERROR, statusCode, details) {
        super(message);
        this.name = 'GoogleCliError';
        this.type = type;
        this.statusCode = statusCode;
        this.details = details;
        // Maintains proper stack trace for where our error was thrown
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, GoogleCliError);
        }
    }
}
exports.GoogleCliError = GoogleCliError;
/**
 * Logger levels
 */
var LogLevel;
(function (LogLevel) {
    LogLevel["ERROR"] = "error";
    LogLevel["WARN"] = "warn";
    LogLevel["INFO"] = "info";
    LogLevel["DEBUG"] = "debug";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
//# sourceMappingURL=index.js.map