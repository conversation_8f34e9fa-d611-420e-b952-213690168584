# Contributing to Google CLI

Thank you for your interest in contributing to Google CLI! This document provides guidelines and information for contributors.

## Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct. Please be respectful and constructive in all interactions.

## How to Contribute

### Reporting Issues

Before creating an issue, please:
1. Check if the issue already exists in our [issue tracker](https://github.com/Sharma-IT/google-cli/issues)
2. Search closed issues to see if it was already resolved
3. Try the latest version to see if the issue persists

When creating an issue, please include:
- **Clear title and description**
- **Steps to reproduce** the issue
- **Expected vs actual behavior**
- **Environment details** (OS, Node.js version, etc.)
- **Error messages** or logs (use `--debug` flag)
- **Screenshots** if applicable

### Suggesting Features

We welcome feature suggestions! Please:
1. Check existing feature requests first
2. Provide a clear use case and rationale
3. Consider the scope and complexity
4. Be open to discussion and feedback

### Pull Requests

1. **Fork the repository** and create a feature branch
2. **Follow the development setup** instructions below
3. **Make your changes** with appropriate tests
4. **Ensure all tests pass** and coverage remains high
5. **Follow code style** guidelines
6. **Update documentation** if needed
7. **Submit a pull request** with a clear description

## Development Setup

### Prerequisites

- Node.js 16.0.0 or higher
- npm or yarn
- Git

### Getting Started

```bash
# Fork and clone the repository
git clone https://github.com/your-username/google-cli.git
cd google-cli

# Install dependencies
npm install

# Copy environment template
cp .env.example .env
# Edit .env with your API credentials

# Build the project
npm run build

# Run tests
npm test
```

### Development Scripts

```bash
# Development
npm run dev              # Run in development mode with ts-node
npm run build            # Build TypeScript to JavaScript
npm run start            # Run the built version

# Testing
npm test                 # Run all tests
npm run test:watch       # Run tests in watch mode
npm run test:coverage    # Run tests with coverage report

# Code Quality
npm run lint             # Run ESLint
npm run lint:fix         # Fix ESLint issues automatically
npm run format           # Format code with Prettier

# Type Checking
npx tsc --noEmit         # Check TypeScript types without building
```

## Code Style Guidelines

### TypeScript

- Use TypeScript for all new code
- Provide proper type annotations
- Avoid `any` types when possible
- Use interfaces for object shapes
- Follow existing naming conventions

### Code Formatting

- Use Prettier for code formatting
- Run `npm run format` before committing
- 2-space indentation
- Single quotes for strings
- Trailing commas in multiline structures

### ESLint Rules

- Follow the existing ESLint configuration
- Fix all linting errors before submitting
- Use `npm run lint:fix` for automatic fixes
- Add `// eslint-disable-next-line` sparingly with justification

### Naming Conventions

- **Files**: kebab-case (e.g., `search-command.ts`)
- **Classes**: PascalCase (e.g., `SearchCommand`)
- **Functions/Variables**: camelCase (e.g., `searchResults`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_BASE_URL`)
- **Interfaces**: PascalCase with descriptive names (e.g., `SearchOptions`)

## Testing Guidelines

### Test Structure

- **Unit tests**: Test individual functions and classes
- **Integration tests**: Test component interactions
- **CLI tests**: Test command-line interface behavior

### Writing Tests

```typescript
describe('ComponentName', () => {
  describe('methodName', () => {
    it('should do something specific', () => {
      // Arrange
      const input = 'test input';
      
      // Act
      const result = component.method(input);
      
      // Assert
      expect(result).toBe('expected output');
    });
  });
});
```

### Test Requirements

- **Coverage**: Maintain >80% test coverage
- **Descriptive names**: Use clear, descriptive test names
- **Isolated tests**: Each test should be independent
- **Mock external dependencies**: Use Jest mocks for API calls
- **Test edge cases**: Include error conditions and boundary cases

### Running Tests

```bash
# Run all tests
npm test

# Run specific test file
npm test -- SearchCommand.test.ts

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

## Documentation

### Code Documentation

- Use JSDoc comments for public APIs
- Include parameter and return type descriptions
- Provide usage examples for complex functions
- Document any non-obvious behavior

```typescript
/**
 * Performs a Google search with the specified options
 * @param options - Search configuration options
 * @returns Promise resolving to search results
 * @throws GoogleCliError when API request fails
 */
public async search(options: SearchOptions): Promise<SearchResult[]> {
  // Implementation
}
```

### README Updates

- Update README.md for new features
- Include usage examples
- Update option tables for new CLI flags
- Add troubleshooting information if needed

### API Documentation

- Update docs/API_SETUP.md for API-related changes
- Include new environment variables
- Document configuration options
- Provide troubleshooting steps

## Commit Guidelines

### Commit Message Format

```
type(scope): description

[optional body]

[optional footer]
```

### Types

- **feat**: New feature
- **fix**: Bug fix
- **docs**: Documentation changes
- **style**: Code style changes (formatting, etc.)
- **refactor**: Code refactoring
- **test**: Adding or updating tests
- **chore**: Maintenance tasks

### Examples

```
feat(search): add language filtering support

Add --language option to filter search results by language.
Includes validation and error handling for invalid language codes.

Closes #123
```

```
fix(api): handle network timeout errors gracefully

Improve error handling for network timeouts by providing
user-friendly error messages and retry suggestions.
```

## Release Process

### Version Numbering

We follow [Semantic Versioning](https://semver.org/):
- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Release Checklist

1. Update version in package.json
2. Update CHANGELOG.md
3. Run full test suite
4. Build and test distribution
5. Create GitHub release
6. Publish to npm

## Getting Help

### Development Questions

- Check existing documentation first
- Search closed issues and pull requests
- Ask questions in issue comments
- Reach out to maintainers if needed

### Resources

- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Jest Testing Framework](https://jestjs.io/docs/getting-started)
- [Commander.js CLI Framework](https://github.com/tj/commander.js)
- [Google Custom Search API](https://developers.google.com/custom-search/v1/overview)

## Recognition

Contributors will be recognized in:
- GitHub contributors list
- Release notes for significant contributions
- README acknowledgments section

Thank you for contributing to Google CLI! 🎉
