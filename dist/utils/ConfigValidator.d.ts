import { GoogleSearchConfig } from '../types';
/**
 * Configuration validation result
 */
export interface ValidationResult {
    isValid: boolean;
    message: string;
    missingFields?: string[];
}
/**
 * Validates configuration for the Google CLI
 */
export declare class ConfigValidator {
    /**
     * Validate the Google Search API configuration
     */
    validateConfig(): ValidationResult;
    /**
     * Get the Google Search configuration from environment variables
     */
    getConfig(): GoogleSearchConfig;
    /**
     * Create a helpful message for missing configuration
     */
    private createMissingConfigMessage;
    /**
     * Basic validation for Google API key format
     */
    private isValidApiKeyFormat;
    /**
     * Basic validation for Google Search Engine ID format
     */
    private isValidSearchEngineIdFormat;
    /**
     * Validate network configuration
     */
    validateNetworkConfig(): ValidationResult;
    /**
     * Check if configuration is for testing
     */
    isTestEnvironment(): boolean;
}
//# sourceMappingURL=ConfigValidator.d.ts.map