"use strict";
/**
 * Jest test setup file
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.testUtils = void 0;
// Set test environment variables
process.env['NODE_ENV'] = 'test';
process.env['NO_COLOR'] = '1';
process.env['GOOGLE_API_KEY'] = 'test-api-key';
process.env['GOOGLE_SEARCH_ENGINE_ID'] = 'test-search-engine-id';
// Mock console methods to reduce noise in tests
const originalConsole = { ...console };
beforeEach(() => {
    // Reset console mocks before each test
    console.log = jest.fn();
    console.error = jest.fn();
    console.warn = jest.fn();
    console.info = jest.fn();
});
afterEach(() => {
    // Restore console after each test
    Object.assign(console, originalConsole);
});
// Test utilities
exports.testUtils = {
    createMockSearchResult: (overrides = {}) => ({
        title: 'Test Result Title',
        link: 'https://example.com/test',
        snippet: 'This is a test search result snippet.',
        displayLink: 'example.com',
        formattedUrl: 'https://example.com/test',
        ...overrides
    }),
    createMockApiResponse: (overrides = {}) => ({
        kind: 'customsearch#search',
        url: {
            type: 'application/json',
            template: 'https://www.googleapis.com/customsearch/v1?q={searchTerms}'
        },
        queries: {
            request: [{
                    title: 'Google Custom Search - test query',
                    totalResults: '1000',
                    searchTerms: 'test query',
                    count: 10,
                    startIndex: 1,
                    inputEncoding: 'utf8',
                    outputEncoding: 'utf8',
                    safe: 'off',
                    cx: 'test-search-engine-id'
                }]
        },
        context: {
            title: 'Test Search Engine'
        },
        searchInformation: {
            searchTime: 0.123456,
            formattedSearchTime: '0.12',
            totalResults: '1000',
            formattedTotalResults: '1,000'
        },
        items: [
            {
                title: 'Test Result Title',
                link: 'https://example.com/test',
                snippet: 'This is a test search result snippet.',
                displayLink: 'example.com',
                formattedUrl: 'https://example.com/test'
            }
        ],
        ...overrides
    })
};
// Custom Jest matchers
expect.extend({
    toBeValidSearchResult(received) {
        const pass = received &&
            typeof received.title === 'string' &&
            typeof received.link === 'string' &&
            typeof received.snippet === 'string';
        if (pass) {
            return {
                message: () => `expected ${JSON.stringify(received)} not to be a valid search result`,
                pass: true,
            };
        }
        else {
            return {
                message: () => `expected ${JSON.stringify(received)} to be a valid search result`,
                pass: false,
            };
        }
    },
    toBeValidApiResponse(received) {
        const pass = received &&
            received.searchInformation &&
            Array.isArray(received.items);
        if (pass) {
            return {
                message: () => `expected ${JSON.stringify(received)} not to be a valid API response`,
                pass: true,
            };
        }
        else {
            return {
                message: () => `expected ${JSON.stringify(received)} to be a valid API response`,
                pass: false,
            };
        }
    },
});
//# sourceMappingURL=setup.js.map