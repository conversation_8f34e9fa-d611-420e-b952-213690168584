"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EdgeCaseHandler = void 0;
const types_1 = require("../types");
/**
 * Handles edge cases and validates inputs
 */
class EdgeCaseHandler {
    constructor(logger) {
        this.logger = logger;
    }
    /**
     * Validate and sanitize search query
     */
    validateSearchQuery(query) {
        if (!query || typeof query !== 'string') {
            throw new types_1.GoogleCliError('Search query must be a non-empty string', types_1.ErrorType.VALIDATION_ERROR);
        }
        // Trim whitespace
        const trimmed = query.trim();
        if (trimmed.length === 0) {
            throw new types_1.GoogleCliError('Search query cannot be empty or contain only whitespace', types_1.ErrorType.VALIDATION_ERROR);
        }
        if (trimmed.length > 2048) {
            throw new types_1.GoogleCliError('Search query is too long (maximum 2048 characters)', types_1.ErrorType.VALIDATION_ERROR);
        }
        // Check for potentially problematic characters
        const problematicChars = /[<>{}[\]\\]/;
        if (problematicChars.test(trimmed)) {
            this.logger.warn('Search query contains special characters that may affect results');
        }
        // Sanitize query
        return this.sanitizeQuery(trimmed);
    }
    /**
     * Sanitize search query
     */
    sanitizeQuery(query) {
        return query
            .replace(/\s+/g, ' ') // Normalize whitespace
            .replace(/[^\w\s"'-]/g, ' ') // Remove most special characters except quotes and hyphens
            .trim();
    }
    /**
     * Handle network connectivity issues
     */
    async checkNetworkConnectivity() {
        try {
            // Simple connectivity check using DNS resolution
            const { promisify } = require('util');
            const dns = require('dns');
            const lookup = promisify(dns.lookup);
            await lookup('google.com');
            return true;
        }
        catch (error) {
            this.logger.debug('Network connectivity check failed', error);
            return false;
        }
    }
    /**
     * Handle environment-specific issues
     */
    validateEnvironment() {
        // Check Node.js version
        const nodeVersion = process.version;
        const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0], 10);
        if (majorVersion < 16) {
            this.logger.warn(`Node.js version ${nodeVersion} is not officially supported. Please upgrade to Node.js 16 or later.`);
        }
        // Check for required modules
        this.checkRequiredModules();
        // Check terminal capabilities
        this.checkTerminalCapabilities();
    }
    /**
     * Check if required modules are available
     */
    checkRequiredModules() {
        const requiredModules = ['axios', 'chalk', 'commander'];
        for (const module of requiredModules) {
            try {
                require(module);
            }
            catch (error) {
                throw new types_1.GoogleCliError(`Required module '${module}' is not available. Please run 'npm install' to install dependencies.`, types_1.ErrorType.VALIDATION_ERROR);
            }
        }
    }
    /**
     * Check terminal capabilities
     */
    checkTerminalCapabilities() {
        // Check if running in a TTY
        if (!process.stdout.isTTY) {
            this.logger.debug('Not running in a TTY environment');
        }
        // Check terminal width
        const width = process.stdout.columns;
        if (!width || width < 40) {
            this.logger.warn('Terminal width is very narrow. Output may not display correctly.');
        }
        // Check color support
        if (process.env.NO_COLOR || process.env.NODE_ENV === 'test') {
            this.logger.debug('Color output disabled');
        }
    }
    /**
     * Handle API quota and rate limiting edge cases
     */
    handleQuotaExceeded(error) {
        const message = error.message || '';
        if (message.includes('quota')) {
            if (message.includes('daily')) {
                return new types_1.GoogleCliError('Daily API quota exceeded. Your quota will reset at midnight Pacific Time.', types_1.ErrorType.RATE_LIMIT_ERROR, error.statusCode, error);
            }
            if (message.includes('per 100 seconds')) {
                return new types_1.GoogleCliError('API rate limit exceeded (100 requests per 100 seconds). Please wait before making another request.', types_1.ErrorType.RATE_LIMIT_ERROR, error.statusCode, error);
            }
        }
        return new types_1.GoogleCliError('API quota or rate limit exceeded. Please check your usage and try again later.', types_1.ErrorType.RATE_LIMIT_ERROR, error.statusCode, error);
    }
    /**
     * Handle malformed API responses
     */
    validateApiResponse(response) {
        if (!response) {
            throw new types_1.GoogleCliError('Received empty response from Google API', types_1.ErrorType.API_ERROR);
        }
        if (typeof response !== 'object') {
            throw new types_1.GoogleCliError('Received invalid response format from Google API', types_1.ErrorType.API_ERROR);
        }
        // Check for API error in response
        if (response.error) {
            const errorMessage = response.error.message || 'Unknown API error';
            const errorCode = response.error.code || 'UNKNOWN';
            throw new types_1.GoogleCliError(`Google API error (${errorCode}): ${errorMessage}`, types_1.ErrorType.API_ERROR, response.error.code);
        }
        // Validate expected structure
        if (!response.searchInformation && !response.items) {
            this.logger.warn('API response missing expected fields');
        }
    }
    /**
     * Handle timeout scenarios
     */
    createTimeoutError(timeout) {
        return new types_1.GoogleCliError(`Request timed out after ${timeout}ms. This may be due to network issues or API overload.`, types_1.ErrorType.NETWORK_ERROR);
    }
    /**
     * Handle SSL/TLS certificate issues
     */
    handleSSLError(error) {
        const message = error.message || '';
        if (message.includes('certificate') || message.includes('SSL') || message.includes('TLS')) {
            return new types_1.GoogleCliError('SSL/TLS certificate error. This may be due to network security settings or an outdated Node.js version.', types_1.ErrorType.NETWORK_ERROR, undefined, error);
        }
        return new types_1.GoogleCliError('Network security error occurred', types_1.ErrorType.NETWORK_ERROR, undefined, error);
    }
    /**
     * Handle proxy and firewall issues
     */
    handleProxyError(error) {
        const message = error.message || '';
        if (message.includes('proxy') || message.includes('ECONNREFUSED') || message.includes('ENOTFOUND')) {
            return new types_1.GoogleCliError('Unable to connect to Google API. This may be due to proxy settings, firewall restrictions, or network connectivity issues.', types_1.ErrorType.NETWORK_ERROR, undefined, error);
        }
        return new types_1.GoogleCliError('Network connection error occurred', types_1.ErrorType.NETWORK_ERROR, undefined, error);
    }
    /**
     * Suggest recovery actions based on error type
     */
    suggestRecoveryActions(error) {
        const suggestions = [];
        switch (error.type) {
            case types_1.ErrorType.NETWORK_ERROR:
                suggestions.push('Check your internet connection');
                suggestions.push('Try again in a few moments');
                suggestions.push('Check if you\'re behind a proxy or firewall');
                break;
            case types_1.ErrorType.API_ERROR:
                suggestions.push('Verify your API key is correct');
                suggestions.push('Check if the Custom Search API is enabled');
                suggestions.push('Ensure your search engine ID is valid');
                break;
            case types_1.ErrorType.RATE_LIMIT_ERROR:
                suggestions.push('Wait before making another request');
                suggestions.push('Consider upgrading your API quota');
                suggestions.push('Reduce the frequency of requests');
                break;
            case types_1.ErrorType.AUTHENTICATION_ERROR:
                suggestions.push('Check your GOOGLE_API_KEY environment variable');
                suggestions.push('Check your GOOGLE_SEARCH_ENGINE_ID environment variable');
                suggestions.push('Verify your API key has the necessary permissions');
                break;
            case types_1.ErrorType.VALIDATION_ERROR:
                suggestions.push('Check your command arguments');
                suggestions.push('Ensure your search query is valid');
                suggestions.push('Review the help documentation');
                break;
            default:
                suggestions.push('Try the command again');
                suggestions.push('Check the debug output for more details');
                break;
        }
        return suggestions;
    }
    /**
     * Check if error is recoverable with retry
     */
    isRecoverableError(error) {
        switch (error.type) {
            case types_1.ErrorType.NETWORK_ERROR:
            case types_1.ErrorType.RATE_LIMIT_ERROR:
                return true;
            case types_1.ErrorType.API_ERROR:
                // Some API errors are recoverable (5xx status codes)
                return error.statusCode ? error.statusCode >= 500 : false;
            default:
                return false;
        }
    }
    /**
     * Calculate appropriate retry delay
     */
    calculateRetryDelay(attempt, error) {
        const baseDelay = 1000; // 1 second
        const maxDelay = 30000; // 30 seconds
        if (error.type === types_1.ErrorType.RATE_LIMIT_ERROR) {
            // Longer delays for rate limit errors
            return Math.min(baseDelay * Math.pow(2, attempt) * 2, maxDelay);
        }
        // Standard exponential backoff
        return Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
    }
}
exports.EdgeCaseHandler = EdgeCaseHandler;
//# sourceMappingURL=EdgeCaseHandler.js.map