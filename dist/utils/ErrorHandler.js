"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorHandler = void 0;
const types_1 = require("../types");
/**
 * Centralized error handling for the CLI application
 */
class ErrorHandler {
    constructor(logger) {
        this.logger = logger;
    }
    /**
     * Handle different types of errors with appropriate messaging
     */
    handleError(error, context) {
        const contextPrefix = context ? `[${context}] ` : '';
        if (error instanceof types_1.GoogleCliError) {
            this.handleGoogleCliError(error, contextPrefix);
        }
        else {
            this.handleGenericError(error, contextPrefix);
        }
    }
    /**
     * Handle Google CLI specific errors
     */
    handleGoogleCliError(error, contextPrefix) {
        switch (error.type) {
            case types_1.ErrorType.NETWORK_ERROR:
                this.logger.error(`${contextPrefix}Network error: ${error.message}`, error);
                this.logger.info('Please check your internet connection and try again.');
                break;
            case types_1.ErrorType.API_ERROR:
                this.logger.error(`${contextPrefix}Google API error: ${error.message}`, error);
                if (error.statusCode === 403) {
                    this.logger.info('This might be due to API quota limits or invalid credentials.');
                    this.logger.info('Please check your API key and search engine ID configuration.');
                }
                break;
            case types_1.ErrorType.RATE_LIMIT_ERROR:
                this.logger.error(`${contextPrefix}Rate limit exceeded: ${error.message}`, error);
                this.logger.info('Please wait a moment before making another request.');
                break;
            case types_1.ErrorType.AUTHENTICATION_ERROR:
                this.logger.error(`${contextPrefix}Authentication error: ${error.message}`, error);
                this.logger.info('Please check your Google API credentials:');
                this.logger.info('1. Ensure GOOGLE_API_KEY is set in your environment');
                this.logger.info('2. Ensure GOOGLE_SEARCH_ENGINE_ID is set in your environment');
                this.logger.info('3. Verify your API key has the Custom Search API enabled');
                break;
            case types_1.ErrorType.VALIDATION_ERROR:
                this.logger.error(`${contextPrefix}Validation error: ${error.message}`, error);
                this.logger.info('Please check your command arguments and try again.');
                break;
            default:
                this.logger.error(`${contextPrefix}Unknown error: ${error.message}`, error);
        }
        // Show additional details in debug mode
        if (error.details) {
            this.logger.debug('Error details:', error.details);
        }
    }
    /**
     * Handle generic JavaScript errors
     */
    handleGenericError(error, contextPrefix) {
        this.logger.error(`${contextPrefix}${error.message}`, error);
        // Provide helpful suggestions for common errors
        if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
            this.logger.info('This appears to be a network connectivity issue.');
            this.logger.info('Please check your internet connection and try again.');
        }
        else if (error.message.includes('timeout')) {
            this.logger.info('The request timed out. Please try again.');
        }
        else if (error.message.includes('EACCES') || error.message.includes('permission')) {
            this.logger.info('Permission denied. Please check file/directory permissions.');
        }
    }
    /**
     * Create a GoogleCliError from a generic error
     */
    createError(message, type = types_1.ErrorType.UNKNOWN_ERROR, originalError, statusCode) {
        const error = new types_1.GoogleCliError(message, type, statusCode, originalError);
        // Preserve original stack trace if available
        if (originalError && originalError.stack) {
            error.stack = originalError.stack;
        }
        return error;
    }
    /**
     * Wrap an async function with error handling
     */
    async withErrorHandling(fn, context) {
        try {
            return await fn();
        }
        catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            this.handleError(err, context);
            return null;
        }
    }
    /**
     * Check if error is recoverable
     */
    isRecoverableError(error) {
        if (error instanceof types_1.GoogleCliError) {
            return error.type === types_1.ErrorType.NETWORK_ERROR ||
                error.type === types_1.ErrorType.RATE_LIMIT_ERROR;
        }
        return error.message.includes('timeout') ||
            error.message.includes('ENOTFOUND') ||
            error.message.includes('ECONNREFUSED');
    }
    /**
     * Get user-friendly error message
     */
    getUserFriendlyMessage(error) {
        if (error instanceof types_1.GoogleCliError) {
            switch (error.type) {
                case types_1.ErrorType.NETWORK_ERROR:
                    return 'Unable to connect to Google. Please check your internet connection.';
                case types_1.ErrorType.API_ERROR:
                    return 'Google API error. Please check your configuration.';
                case types_1.ErrorType.RATE_LIMIT_ERROR:
                    return 'Too many requests. Please wait a moment and try again.';
                case types_1.ErrorType.AUTHENTICATION_ERROR:
                    return 'Invalid API credentials. Please check your configuration.';
                case types_1.ErrorType.VALIDATION_ERROR:
                    return 'Invalid input. Please check your command arguments.';
                default:
                    return 'An unexpected error occurred.';
            }
        }
        return error.message || 'An unknown error occurred.';
    }
}
exports.ErrorHandler = ErrorHandler;
//# sourceMappingURL=ErrorHandler.js.map