import { RateLimitConfig } from '../types';
/**
 * Rate limiter to prevent exceeding API quotas
 */
export declare class RateLimiter {
    private config;
    private requestTimes;
    private dailyRequestCount;
    private lastResetDate;
    constructor(config: RateLimitConfig);
    /**
     * Check if a request can be made within rate limits
     */
    checkLimit(): Promise<void>;
    /**
     * Record a successful request
     */
    private recordRequest;
    /**
     * Clean up old request timestamps
     */
    private cleanOldRequests;
    /**
     * Reset daily count if it's a new day
     */
    private resetDailyCountIfNeeded;
    /**
     * Get current rate limit status
     */
    getStatus(): {
        dailyRequestsRemaining: number;
        requestsInLastSecond: number;
        requestsInBurstWindow: number;
    };
    /**
     * Calculate how long to wait before next request
     */
    getWaitTime(): number;
    /**
     * Reset all rate limiting data (useful for testing)
     */
    reset(): void;
}
//# sourceMappingURL=RateLimiter.d.ts.map