"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SearchResultProcessor = void 0;
/**
 * Processes and enhances search results
 */
class SearchResultProcessor {
    /**
     * Process raw search results with filtering and enhancement
     */
    processResults(results, options = {}) {
        let processedResults = [...results];
        // Remove duplicates if requested
        if (options.removeDuplicates) {
            processedResults = this.removeDuplicates(processedResults);
        }
        // Filter spam/low-quality results
        if (options.filterSpam) {
            processedResults = this.filterSpamResults(processedResults);
        }
        // Enhance snippets
        if (options.enhanceSnippets) {
            processedResults = this.enhanceSnippets(processedResults);
        }
        // Sort by relevance
        if (options.sortByRelevance) {
            processedResults = this.sortByRelevance(processedResults);
        }
        // Limit results
        if (options.maxResults && options.maxResults > 0) {
            processedResults = processedResults.slice(0, options.maxResults);
        }
        return processedResults;
    }
    /**
     * Remove duplicate results based on URL and title similarity
     */
    removeDuplicates(results) {
        const seen = new Set();
        const uniqueResults = [];
        for (const result of results) {
            const key = this.generateResultKey(result);
            if (!seen.has(key)) {
                seen.add(key);
                uniqueResults.push(result);
            }
        }
        return uniqueResults;
    }
    /**
     * Generate a unique key for a search result
     */
    generateResultKey(result) {
        // Normalize URL by removing common variations
        const normalizedUrl = this.normalizeUrl(result.link);
        // Normalize title by removing common words and punctuation
        const normalizedTitle = this.normalizeTitle(result.title);
        return `${normalizedUrl}|${normalizedTitle}`;
    }
    /**
     * Normalize URL for comparison
     */
    normalizeUrl(url) {
        try {
            const urlObj = new URL(url);
            // Remove common tracking parameters
            const trackingParams = [
                'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content',
                'fbclid', 'gclid', 'ref', 'source', 'campaign'
            ];
            trackingParams.forEach(param => {
                urlObj.searchParams.delete(param);
            });
            // Remove trailing slash and fragment
            let normalizedPath = urlObj.pathname.replace(/\/$/, '');
            if (normalizedPath === '') {
                normalizedPath = '/';
            }
            return `${urlObj.protocol}//${urlObj.hostname}${normalizedPath}${urlObj.search}`;
        }
        catch {
            // If URL parsing fails, return original URL
            return url.toLowerCase();
        }
    }
    /**
     * Normalize title for comparison
     */
    normalizeTitle(title) {
        return title
            .toLowerCase()
            .replace(/[^\w\s]/g, '') // Remove punctuation
            .replace(/\s+/g, ' ') // Normalize whitespace
            .trim();
    }
    /**
     * Filter out spam and low-quality results
     */
    filterSpamResults(results) {
        return results.filter(result => {
            // Check for spam indicators in title
            if (this.isSpamTitle(result.title)) {
                return false;
            }
            // Check for spam indicators in URL
            if (this.isSpamUrl(result.link)) {
                return false;
            }
            // Check for spam indicators in snippet
            if (this.isSpamSnippet(result.snippet)) {
                return false;
            }
            return true;
        });
    }
    /**
     * Check if title contains spam indicators
     */
    isSpamTitle(title) {
        const spamIndicators = [
            /click here/i,
            /free download/i,
            /100% free/i,
            /make money/i,
            /get rich/i,
            /weight loss/i,
            /miracle cure/i,
            /limited time/i,
            /act now/i
        ];
        return spamIndicators.some(pattern => pattern.test(title));
    }
    /**
     * Check if URL contains spam indicators
     */
    isSpamUrl(url) {
        const spamDomains = [
            'bit.ly',
            'tinyurl.com',
            'goo.gl',
            't.co'
        ];
        const spamPatterns = [
            /\/ads?\//i,
            /\/spam\//i,
            /\/click\//i,
            /\/redirect\//i
        ];
        try {
            const urlObj = new URL(url);
            // Check for spam domains
            if (spamDomains.some(domain => urlObj.hostname.includes(domain))) {
                return true;
            }
            // Check for spam patterns in path
            if (spamPatterns.some(pattern => pattern.test(urlObj.pathname))) {
                return true;
            }
        }
        catch {
            // If URL parsing fails, don't filter it out
            return false;
        }
        return false;
    }
    /**
     * Check if snippet contains spam indicators
     */
    isSpamSnippet(snippet) {
        if (!snippet)
            return false;
        const spamIndicators = [
            /click here to/i,
            /download now/i,
            /free trial/i,
            /no credit card/i,
            /limited time offer/i
        ];
        return spamIndicators.some(pattern => pattern.test(snippet));
    }
    /**
     * Enhance snippets with better formatting and information
     */
    enhanceSnippets(results) {
        return results.map(result => ({
            ...result,
            snippet: this.enhanceSnippet(result.snippet, result.title)
        }));
    }
    /**
     * Enhance a single snippet
     */
    enhanceSnippet(snippet, title) {
        if (!snippet)
            return '';
        let enhanced = snippet;
        // Remove redundant title repetition from snippet
        const titleWords = title.toLowerCase().split(/\s+/);
        const snippetWords = enhanced.toLowerCase().split(/\s+/);
        // If snippet starts with title words, remove them
        let startIndex = 0;
        for (let i = 0; i < Math.min(titleWords.length, snippetWords.length); i++) {
            if (titleWords[i] === snippetWords[i]) {
                startIndex = i + 1;
            }
            else {
                break;
            }
        }
        if (startIndex > 2) {
            const originalWords = enhanced.split(/\s+/);
            enhanced = originalWords.slice(startIndex).join(' ');
        }
        // Clean up common snippet artifacts
        enhanced = enhanced
            .replace(/^[.,:;-]\s*/, '') // Remove leading punctuation
            .replace(/\s*\.\.\.\s*$/, '...') // Normalize trailing ellipsis
            .replace(/\s+/g, ' ') // Normalize whitespace
            .trim();
        return enhanced;
    }
    /**
     * Sort results by relevance score
     */
    sortByRelevance(results) {
        return results
            .map(result => ({
            result,
            score: this.calculateRelevanceScore(result)
        }))
            .sort((a, b) => b.score - a.score)
            .map(item => item.result);
    }
    /**
     * Calculate relevance score for a result
     */
    calculateRelevanceScore(result) {
        let score = 0;
        // Domain authority (simplified heuristic)
        score += this.getDomainAuthorityScore(result.link);
        // Title quality
        score += this.getTitleQualityScore(result.title);
        // Snippet quality
        score += this.getSnippetQualityScore(result.snippet);
        // URL quality
        score += this.getUrlQualityScore(result.link);
        return score;
    }
    /**
     * Get domain authority score (simplified)
     */
    getDomainAuthorityScore(url) {
        try {
            const urlObj = new URL(url);
            const domain = urlObj.hostname.toLowerCase();
            // High authority domains
            const highAuthority = [
                'wikipedia.org', 'github.com', 'stackoverflow.com',
                'mozilla.org', 'w3.org', 'ietf.org', 'rfc-editor.org'
            ];
            // Medium authority domains
            const mediumAuthority = [
                'medium.com', 'dev.to', 'hashnode.com', 'freecodecamp.org'
            ];
            if (highAuthority.some(d => domain.includes(d))) {
                return 10;
            }
            if (mediumAuthority.some(d => domain.includes(d))) {
                return 5;
            }
            // Prefer HTTPS
            if (urlObj.protocol === 'https:') {
                return 2;
            }
            return 0;
        }
        catch {
            return 0;
        }
    }
    /**
     * Get title quality score
     */
    getTitleQualityScore(title) {
        let score = 0;
        // Prefer descriptive titles
        if (title.length > 20 && title.length < 100) {
            score += 3;
        }
        // Penalize all caps
        if (title === title.toUpperCase()) {
            score -= 2;
        }
        // Prefer titles with common technical terms
        const technicalTerms = [
            'tutorial', 'guide', 'documentation', 'reference',
            'example', 'how to', 'best practices', 'introduction'
        ];
        if (technicalTerms.some(term => title.toLowerCase().includes(term))) {
            score += 2;
        }
        return score;
    }
    /**
     * Get snippet quality score
     */
    getSnippetQualityScore(snippet) {
        if (!snippet)
            return 0;
        let score = 0;
        // Prefer informative snippets
        if (snippet.length > 50 && snippet.length < 200) {
            score += 2;
        }
        // Prefer complete sentences
        if (snippet.includes('.') || snippet.includes('!') || snippet.includes('?')) {
            score += 1;
        }
        return score;
    }
    /**
     * Get URL quality score
     */
    getUrlQualityScore(url) {
        let score = 0;
        try {
            const urlObj = new URL(url);
            // Prefer clean URLs
            if (urlObj.pathname.split('/').length <= 4) {
                score += 1;
            }
            // Penalize URLs with many query parameters
            if (urlObj.searchParams.toString().length > 100) {
                score -= 1;
            }
            // Prefer common file extensions for documentation
            const goodExtensions = ['.html', '.htm', '.md', '.pdf'];
            if (goodExtensions.some(ext => urlObj.pathname.endsWith(ext))) {
                score += 1;
            }
        }
        catch {
            // Invalid URL
            score -= 5;
        }
        return score;
    }
}
exports.SearchResultProcessor = SearchResultProcessor;
//# sourceMappingURL=SearchResultProcessor.js.map