#!/usr/bin/env node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.main = main;
const commander_1 = require("commander");
const chalk_1 = __importDefault(require("chalk"));
const dotenv_1 = require("dotenv");
const SearchCommand_1 = require("./commands/SearchCommand");
const ErrorHandler_1 = require("./utils/ErrorHandler");
const Logger_1 = require("./utils/Logger");
// Load environment variables
(0, dotenv_1.config)();
const program = new commander_1.Command();
const logger = new Logger_1.Logger();
const errorHandler = new ErrorHandler_1.ErrorHandler(logger);
// Set up global error handling
process.on('uncaughtException', (error) => {
    errorHandler.handleError(error, 'Uncaught Exception');
    process.exit(1);
});
process.on('unhandledRejection', (reason) => {
    const error = reason instanceof Error ? reason : new Error(String(reason));
    errorHandler.handleError(error, 'Unhandled Rejection');
    process.exit(1);
});
async function main() {
    try {
        // Configure the main program
        program
            .name('google-cli')
            .description('A command-line interface tool for performing Google web searches')
            .version('1.0.0', '-v, --version', 'display version number')
            .helpOption('-h, --help', 'display help for command');
        // Add search command
        const searchCommand = new SearchCommand_1.SearchCommand(logger, errorHandler);
        searchCommand.register(program);
        // Add global options
        program
            .option('-d, --debug', 'enable debug mode')
            .option('--no-color', 'disable colored output')
            .hook('preAction', (thisCommand) => {
            const opts = thisCommand.opts();
            if (opts.debug) {
                logger.setDebugMode(true);
            }
            if (opts.noColor) {
                process.env.NO_COLOR = '1';
            }
        });
        // Custom help
        program.configureHelp({
            sortSubcommands: true,
            subcommandTerm: (cmd) => cmd.name(),
        });
        // Parse command line arguments
        await program.parseAsync(process.argv);
        // If no arguments provided, show help
        if (process.argv.length <= 2) {
            program.help();
        }
    }
    catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        errorHandler.handleError(err, 'CLI Initialization');
        process.exit(1);
    }
}
// Only run if this file is executed directly
if (require.main === module) {
    main().catch((error) => {
        console.error(chalk_1.default.red('Fatal error:'), error.message);
        process.exit(1);
    });
}
//# sourceMappingURL=index.js.map