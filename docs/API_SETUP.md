# Google API Setup Guide

This guide will walk you through setting up the Google Custom Search API for use with Google CLI.

## Prerequisites

- A Google account
- Access to Google Cloud Console
- Basic familiarity with environment variables

## Step 1: Create a Google Cloud Project

1. **Go to Google Cloud Console**:
   - Visit [https://console.cloud.google.com/](https://console.cloud.google.com/)
   - Sign in with your Google account

2. **Create a New Project** (or select an existing one):
   - Click on the project dropdown at the top of the page
   - Click "New Project"
   - Enter a project name (e.g., "Google CLI Search")
   - Click "Create"

3. **Select Your Project**:
   - Make sure your new project is selected in the project dropdown

## Step 2: Enable the Custom Search API

1. **Navigate to APIs & Services**:
   - In the left sidebar, click "APIs & Services" > "Library"

2. **Search for Custom Search API**:
   - In the search box, type "Custom Search API"
   - Click on "Custom Search API" from the results

3. **Enable the API**:
   - Click the "Enable" button
   - Wait for the API to be enabled (this may take a few moments)

## Step 3: Create API Credentials

1. **Go to Credentials**:
   - In the left sidebar, click "APIs & Services" > "Credentials"

2. **Create Credentials**:
   - Click "Create Credentials" > "API key"
   - Your API key will be generated and displayed

3. **Secure Your API Key** (Recommended):
   - Click "Restrict Key" to add restrictions
   - Under "API restrictions", select "Restrict key"
   - Choose "Custom Search API" from the dropdown
   - Click "Save"

4. **Copy Your API Key**:
   - Copy the API key and store it securely
   - You'll need this for the `GOOGLE_API_KEY` environment variable

## Step 4: Create a Custom Search Engine

1. **Go to Custom Search Engine**:
   - Visit [https://cse.google.com/cse/](https://cse.google.com/cse/)
   - Sign in with the same Google account

2. **Create a New Search Engine**:
   - Click "Add" or "Create a custom search engine"

3. **Configure Your Search Engine**:
   - **Sites to search**: Enter `*` to search the entire web
   - **Language**: Choose your preferred language
   - **Name**: Give your search engine a name (e.g., "Google CLI Search")

4. **Create the Search Engine**:
   - Click "Create"

5. **Get Your Search Engine ID**:
   - After creation, you'll see your search engine listed
   - Click on your search engine name
   - In the "Setup" tab, you'll see "Search engine ID"
   - Copy this ID - you'll need it for `GOOGLE_SEARCH_ENGINE_ID`

## Step 5: Configure Environment Variables

### Option 1: Export Environment Variables

Add these lines to your shell profile (`.bashrc`, `.zshrc`, etc.):

```bash
export GOOGLE_API_KEY="your-api-key-here"
export GOOGLE_SEARCH_ENGINE_ID="your-search-engine-id-here"
```

Then reload your shell:
```bash
source ~/.bashrc  # or ~/.zshrc
```

### Option 2: Create a .env File

In your project directory, create a `.env` file:

```env
GOOGLE_API_KEY=your-api-key-here
GOOGLE_SEARCH_ENGINE_ID=your-search-engine-id-here
```

## Step 6: Test Your Setup

Run a test search to verify everything is working:

```bash
google-cli "test search"
```

If configured correctly, you should see search results. If not, check the troubleshooting section below.

## API Quotas and Limits

### Free Tier Limits
- **100 queries per day** for free
- **10 queries per second**
- No credit card required for free tier

### Paid Tier
- **$5 per 1,000 queries** after free tier
- **10,000 queries per day** maximum
- Requires billing account setup

### Rate Limiting
Google CLI includes built-in rate limiting to prevent exceeding quotas:
- Respects the 10 queries per second limit
- Tracks daily usage
- Provides helpful error messages when limits are reached

## Security Best Practices

1. **Restrict Your API Key**:
   - Always restrict API keys to specific APIs
   - Consider IP restrictions for production use

2. **Keep Credentials Secure**:
   - Never commit API keys to version control
   - Use environment variables or secure credential storage
   - Rotate keys regularly

3. **Monitor Usage**:
   - Check your API usage in Google Cloud Console
   - Set up billing alerts if using paid tier

## Troubleshooting

### Common Issues

#### "Missing required configuration" Error
- **Cause**: Environment variables not set
- **Solution**: Verify `GOOGLE_API_KEY` and `GOOGLE_SEARCH_ENGINE_ID` are set correctly

#### "Authentication failed" Error
- **Cause**: Invalid API key or insufficient permissions
- **Solution**: 
  - Verify API key is correct
  - Ensure Custom Search API is enabled
  - Check API key restrictions

#### "API quota exceeded" Error
- **Cause**: Exceeded daily or per-second limits
- **Solution**: 
  - Wait for quota reset (daily limits reset at midnight Pacific Time)
  - Consider upgrading to paid tier
  - Reduce search frequency

#### "Invalid search engine ID" Error
- **Cause**: Incorrect Custom Search Engine ID
- **Solution**: 
  - Verify the search engine ID from Google CSE console
  - Ensure the search engine is configured to search the entire web

### Getting Help

If you're still having issues:

1. **Check the logs**: Run with `--debug` flag for detailed error information
2. **Verify credentials**: Double-check your API key and search engine ID
3. **Test in browser**: Try your Custom Search Engine directly in the CSE console
4. **Check quotas**: Verify you haven't exceeded API limits in Google Cloud Console

## Advanced Configuration

### Custom API Base URL
If you need to use a different API endpoint:
```bash
export GOOGLE_API_BASE_URL="https://custom-api-endpoint.com"
```

### Timeout and Retry Settings
```bash
export GOOGLE_API_TIMEOUT="15000"  # 15 seconds
export GOOGLE_API_RETRIES="5"     # 5 retry attempts
```

### Search Engine Configuration

For more advanced search configurations:

1. **Site-specific search**: Instead of `*`, specify particular domains
2. **Language settings**: Configure default language in CSE console
3. **Safe search**: Set default safe search level in CSE console
4. **Custom styling**: Customize result appearance (for web use)

## Cost Optimization

### Minimizing Costs
1. **Use appropriate result counts**: Don't request more results than needed
2. **Cache results**: Implement local caching for repeated searches
3. **Monitor usage**: Set up billing alerts in Google Cloud Console
4. **Optimize queries**: Use specific search terms to get better results with fewer requests

### Free Tier Tips
- The free tier provides 100 searches per day
- Perfect for personal use and development
- Consider multiple projects for higher free tier limits (not recommended for production)

## Next Steps

Once your API is set up:
1. Explore different search options with `google-cli --help`
2. Try different output formats (`--format json`, `--format compact`)
3. Experiment with language and country settings
4. Set up shell aliases for common searches

For more information, see the main [README.md](../README.md) file.
