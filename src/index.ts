#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import { config } from 'dotenv';
import { SearchCommand } from './commands/SearchCommand';
import { ErrorHandler } from './utils/ErrorHandler';
import { Logger } from './utils/Logger';

// Load environment variables
config();

const program = new Command();
const logger = new Logger();
const errorHandler = new ErrorHandler(logger);

// Set up global error handling
process.on('uncaughtException', (error: Error) => {
  errorHandler.handleError(error, 'Uncaught Exception');
  process.exit(1);
});

process.on('unhandledRejection', (reason: unknown) => {
  const error = reason instanceof Error ? reason : new Error(String(reason));
  errorHandler.handleError(error, 'Unhandled Rejection');
  process.exit(1);
});

async function main(): Promise<void> {
  try {
    // Configure the main program
    program
      .name('google-cli')
      .description('A command-line interface tool for performing Google web searches')
      .version('1.0.0', '-v, --version', 'display version number')
      .helpOption('-h, --help', 'display help for command');

    // Add search command
    const searchCommand = new SearchCommand(logger, errorHandler);
    searchCommand.register(program);

    // Add global options
    program
      .option('-d, --debug', 'enable debug mode')
      .option('--no-color', 'disable colored output')
      .hook('preAction', (thisCommand) => {
        const opts = thisCommand.opts();
        if (opts.debug) {
          logger.setDebugMode(true);
        }
        if (opts.noColor) {
          process.env.NO_COLOR = '1';
        }
      });

    // Custom help
    program.configureHelp({
      sortSubcommands: true,
      subcommandTerm: (cmd) => cmd.name(),
    });

    // If no arguments provided, show help
    if (process.argv.length <= 2) {
      program.help();
      return;
    }

    // Parse command line arguments
    await program.parseAsync(process.argv);
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    errorHandler.handleError(err, 'CLI Initialization');
    process.exit(1);
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  main().catch((error) => {
    console.error(chalk.red('Fatal error:'), error.message);
    process.exit(1);
  });
}

export { main };
