"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResultFormatter = void 0;
const chalk_1 = __importDefault(require("chalk"));
/**
 * Formats search results for terminal display
 */
class ResultFormatter {
    constructor(logger) {
        this.logger = logger;
        this.terminalWidth = process.stdout.columns || 80;
    }
    /**
     * Format and display search results
     */
    async formatResults(results, options = {}) {
        const opts = this.mergeOptions(options);
        switch (opts.format) {
            case 'json':
                this.formatAsJson(results);
                break;
            case 'compact':
                this.formatAsCompact(results, opts);
                break;
            case 'table':
            default:
                this.formatAsTable(results, opts);
                break;
        }
    }
    /**
     * Merge default options with provided options
     */
    mergeOptions(options) {
        return {
            width: options.width || this.terminalWidth,
            maxSnippetLength: options.maxSnippetLength || 150,
            showNumbers: options.showNumbers !== false,
            compact: options.compact || false,
            noColor: options.noColor || false,
            format: options.format || 'table'
        };
    }
    /**
     * Format results as JSON
     */
    formatAsJson(results) {
        console.log(JSON.stringify(results, null, 2));
    }
    /**
     * Format results as a compact list
     */
    formatAsCompact(results, options) {
        results.forEach((result, index) => {
            const number = options.showNumbers ? `${index + 1}. ` : '';
            const title = this.formatTitle(result.title, options.noColor);
            const url = this.formatUrl(result.link, options.noColor);
            console.log(`${number}${title}`);
            console.log(`${url}`);
            if (index < results.length - 1) {
                console.log(''); // Empty line between results
            }
        });
    }
    /**
     * Format results as a detailed table
     */
    formatAsTable(results, options) {
        const divider = this.createDivider(options.width, options.noColor);
        console.log(divider);
        console.log(this.formatHeader('Search Results', options));
        console.log(divider);
        results.forEach((result, index) => {
            this.formatSingleResult(result, index + 1, options);
            if (index < results.length - 1) {
                console.log(this.createDivider(options.width, options.noColor, '─'));
            }
        });
        console.log(divider);
    }
    /**
     * Format a single search result
     */
    formatSingleResult(result, number, options) {
        // Result number and title
        const numberStr = options.showNumbers ?
            (options.noColor ? `[${number}] ` : chalk_1.default.cyan.bold(`[${number}] `)) : '';
        const title = this.formatTitle(result.title, options.noColor);
        console.log(`${numberStr}${title}`);
        // URL
        const url = this.formatUrl(result.link, options.noColor);
        console.log(url);
        // Snippet
        if (result.snippet) {
            const snippet = this.formatSnippet(result.snippet, options);
            console.log(snippet);
        }
        console.log(''); // Empty line after each result
    }
    /**
     * Format the title with appropriate styling
     */
    formatTitle(title, noColor) {
        const wrappedTitle = this.wrapText(title, this.terminalWidth - 8);
        return noColor ? wrappedTitle : chalk_1.default.blue.bold(wrappedTitle);
    }
    /**
     * Format the URL with appropriate styling
     */
    formatUrl(url, noColor) {
        const maxUrlLength = this.terminalWidth - 4;
        const truncatedUrl = url.length > maxUrlLength ?
            `${url.substring(0, maxUrlLength - 3)}...` : url;
        return noColor ? truncatedUrl : chalk_1.default.green(truncatedUrl);
    }
    /**
     * Format the snippet with appropriate styling and length
     */
    formatSnippet(snippet, options) {
        let processedSnippet = snippet;
        // Truncate if too long
        if (processedSnippet.length > options.maxSnippetLength) {
            processedSnippet = `${processedSnippet.substring(0, options.maxSnippetLength - 3)}...`;
        }
        // Wrap text to terminal width
        const wrappedSnippet = this.wrapText(processedSnippet, options.width - 4);
        // Add indentation
        const indentedSnippet = wrappedSnippet
            .split('\n')
            .map(line => `  ${line}`)
            .join('\n');
        return options.noColor ? indentedSnippet : chalk_1.default.gray(indentedSnippet);
    }
    /**
     * Format the header
     */
    formatHeader(text, options) {
        const centeredText = this.centerText(text, options.width);
        return options.noColor ? centeredText : chalk_1.default.yellow.bold(centeredText);
    }
    /**
     * Create a divider line
     */
    createDivider(width, noColor, char = '=') {
        const line = char.repeat(width);
        return noColor ? line : chalk_1.default.gray(line);
    }
    /**
     * Center text within a given width
     */
    centerText(text, width) {
        const padding = Math.max(0, Math.floor((width - text.length) / 2));
        return ' '.repeat(padding) + text;
    }
    /**
     * Wrap text to fit within specified width
     */
    wrapText(text, width) {
        if (text.length <= width) {
            return text;
        }
        const words = text.split(' ');
        const lines = [];
        let currentLine = '';
        for (const word of words) {
            const testLine = currentLine ? `${currentLine} ${word}` : word;
            if (testLine.length <= width) {
                currentLine = testLine;
            }
            else {
                if (currentLine) {
                    lines.push(currentLine);
                    currentLine = word;
                }
                else {
                    // Word is longer than width, force break
                    lines.push(word.substring(0, width));
                    currentLine = word.substring(width);
                }
            }
        }
        if (currentLine) {
            lines.push(currentLine);
        }
        return lines.join('\n');
    }
    /**
     * Highlight search terms in text (for future enhancement)
     */
    highlightSearchTerms(text, searchTerms, noColor) {
        if (noColor || !searchTerms.length) {
            return text;
        }
        let highlightedText = text;
        searchTerms.forEach(term => {
            const regex = new RegExp(`(${this.escapeRegex(term)})`, 'gi');
            highlightedText = highlightedText.replace(regex, chalk_1.default.yellow.bold('$1'));
        });
        return highlightedText;
    }
    /**
     * Escape special regex characters
     */
    escapeRegex(text) {
        return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    /**
     * Format results count summary
     */
    formatResultsSummary(count, query, searchTime, noColor = false) {
        const timeStr = searchTime ? ` in ${searchTime.toFixed(2)}s` : '';
        const summary = `Found ${count} result(s) for "${query}"${timeStr}`;
        return noColor ? summary : chalk_1.default.cyan(summary);
    }
    /**
     * Format error message for display
     */
    formatError(message, noColor = false) {
        return noColor ? `Error: ${message}` : chalk_1.default.red.bold(`Error: ${message}`);
    }
    /**
     * Format warning message for display
     */
    formatWarning(message, noColor = false) {
        return noColor ? `Warning: ${message}` : chalk_1.default.yellow(`Warning: ${message}`);
    }
    /**
     * Format info message for display
     */
    formatInfo(message, noColor = false) {
        return noColor ? message : chalk_1.default.blue(message);
    }
}
exports.ResultFormatter = ResultFormatter;
//# sourceMappingURL=ResultFormatter.js.map