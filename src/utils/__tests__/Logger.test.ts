import { Logger } from '../Logger';
import { LogLevel } from '../../types';

describe('Logger', () => {
  let logger: Logger;
  let consoleSpy: jest.SpyInstance;

  beforeEach(() => {
    logger = new Logger();
    consoleSpy = jest.spyOn(console, 'log').mockImplementation();
  });

  afterEach(() => {
    consoleSpy.mockRestore();
  });

  describe('constructor', () => {
    it('should initialize with correct default settings', () => {
      expect(logger).toBeInstanceOf(Logger);
    });

    it('should respect NO_COLOR environment variable', () => {
      process.env.NO_COLOR = '1';
      const noColorLogger = new Logger();
      expect(noColorLogger).toBeInstanceOf(Logger);
    });
  });

  describe('setDebugMode', () => {
    it('should enable debug mode', () => {
      logger.setDebugMode(true);
      logger.debug('test debug message');
      expect(console.log).toHaveBeenCalledWith(expect.stringContaining('[DEBUG]'));
    });

    it('should disable debug mode', () => {
      logger.setDebugMode(false);
      logger.debug('test debug message');
      expect(console.log).not.toHaveBeenCalled();
    });
  });

  describe('setColorMode', () => {
    it('should enable color mode', () => {
      logger.setColorMode(true);
      logger.info('test message');
      expect(console.log).toHaveBeenCalled();
    });

    it('should disable color mode', () => {
      logger.setColorMode(false);
      logger.info('test message');
      expect(console.log).toHaveBeenCalled();
    });
  });

  describe('error', () => {
    beforeEach(() => {
      consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    });

    it('should log error message', () => {
      logger.error('test error');
      expect(console.error).toHaveBeenCalledWith(expect.stringContaining('[ERROR]'));
      expect(console.error).toHaveBeenCalledWith(expect.stringContaining('test error'));
    });

    it('should log error with stack trace in debug mode', () => {
      logger.setDebugMode(true);
      const error = new Error('test error');
      logger.error('test message', error);
      expect(console.error).toHaveBeenCalledTimes(2);
    });
  });

  describe('warn', () => {
    beforeEach(() => {
      consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
    });

    it('should log warning message', () => {
      logger.warn('test warning');
      expect(console.warn).toHaveBeenCalledWith(expect.stringContaining('[WARN]'));
      expect(console.warn).toHaveBeenCalledWith(expect.stringContaining('test warning'));
    });
  });

  describe('info', () => {
    it('should log info message', () => {
      logger.info('test info');
      expect(console.log).toHaveBeenCalledWith(expect.stringContaining('[INFO]'));
      expect(console.log).toHaveBeenCalledWith(expect.stringContaining('test info'));
    });
  });

  describe('success', () => {
    it('should log success message', () => {
      logger.success('test success');
      expect(console.log).toHaveBeenCalledWith(expect.stringContaining('[SUCCESS]'));
      expect(console.log).toHaveBeenCalledWith(expect.stringContaining('test success'));
    });
  });

  describe('debug', () => {
    it('should not log debug message when debug mode is disabled', () => {
      logger.setDebugMode(false);
      logger.debug('test debug');
      expect(console.log).not.toHaveBeenCalled();
    });

    it('should log debug message when debug mode is enabled', () => {
      logger.setDebugMode(true);
      logger.debug('test debug');
      expect(console.log).toHaveBeenCalledWith(expect.stringContaining('[DEBUG]'));
      expect(console.log).toHaveBeenCalledWith(expect.stringContaining('test debug'));
    });

    it('should log debug message with data', () => {
      logger.setDebugMode(true);
      const data = { key: 'value' };
      logger.debug('test debug', data);
      expect(console.log).toHaveBeenCalledTimes(2);
    });
  });

  describe('log', () => {
    it('should log error level', () => {
      const errorSpy = jest.spyOn(logger, 'error').mockImplementation();
      logger.log(LogLevel.ERROR, 'test message');
      expect(errorSpy).toHaveBeenCalledWith('test message', undefined);
    });

    it('should log warn level', () => {
      const warnSpy = jest.spyOn(logger, 'warn').mockImplementation();
      logger.log(LogLevel.WARN, 'test message');
      expect(warnSpy).toHaveBeenCalledWith('test message');
    });

    it('should log info level', () => {
      const infoSpy = jest.spyOn(logger, 'info').mockImplementation();
      logger.log(LogLevel.INFO, 'test message');
      expect(infoSpy).toHaveBeenCalledWith('test message');
    });

    it('should log debug level', () => {
      const debugSpy = jest.spyOn(logger, 'debug').mockImplementation();
      logger.log(LogLevel.DEBUG, 'test message', { data: 'test' });
      expect(debugSpy).toHaveBeenCalledWith('test message', { data: 'test' });
    });
  });

  describe('divider', () => {
    it('should log divider with default character', () => {
      logger.divider();
      expect(console.log).toHaveBeenCalledWith(expect.stringMatching(/^-+$/));
    });

    it('should log divider with custom character and length', () => {
      logger.divider('=', 10);
      expect(console.log).toHaveBeenCalledWith('==========');
    });
  });

  describe('styled', () => {
    it('should log styled message', () => {
      // Enable color mode for this test
      logger.setColorMode(true);
      const style = (text: string) => `styled: ${text}`;
      logger.styled('test message', style);
      expect(console.log).toHaveBeenCalledWith('styled: test message');
    });

    it('should log plain message when no style provided', () => {
      logger.styled('test message');
      expect(console.log).toHaveBeenCalledWith('test message');
    });

    it('should log plain message when colors are disabled', () => {
      logger.setColorMode(false);
      const style = (text: string) => `styled: ${text}`;
      logger.styled('test message', style);
      expect(console.log).toHaveBeenCalledWith('test message');
    });
  });

  describe('clear', () => {
    it('should clear console when in TTY', () => {
      const clearSpy = jest.spyOn(console, 'clear').mockImplementation();
      const originalIsTTY = process.stdout.isTTY;

      // Mock isTTY property
      Object.defineProperty(process.stdout, 'isTTY', {
        value: true,
        configurable: true
      });

      logger.clear();
      expect(clearSpy).toHaveBeenCalled();

      // Restore original value
      Object.defineProperty(process.stdout, 'isTTY', {
        value: originalIsTTY,
        configurable: true
      });
      clearSpy.mockRestore();
    });

    it('should not clear console when not in TTY', () => {
      const clearSpy = jest.spyOn(console, 'clear').mockImplementation();
      const originalIsTTY = process.stdout.isTTY;

      // Mock isTTY property
      Object.defineProperty(process.stdout, 'isTTY', {
        value: false,
        configurable: true
      });

      logger.clear();
      expect(clearSpy).not.toHaveBeenCalled();

      // Restore original value
      Object.defineProperty(process.stdout, 'isTTY', {
        value: originalIsTTY,
        configurable: true
      });
      clearSpy.mockRestore();
    });
  });
});
