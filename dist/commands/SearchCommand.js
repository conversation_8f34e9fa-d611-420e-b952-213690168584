"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SearchCommand = void 0;
const chalk_1 = __importDefault(require("chalk"));
const types_1 = require("../types");
const GoogleSearchService_1 = require("../services/GoogleSearchService");
const ResultFormatter_1 = require("../utils/ResultFormatter");
const ConfigValidator_1 = require("../utils/ConfigValidator");
/**
 * Search command implementation
 */
class SearchCommand {
    constructor(logger, errorHandler) {
        this.logger = logger;
        this.errorHandler = errorHandler;
        this.searchService = new GoogleSearchService_1.GoogleSearchService(logger, errorHandler);
        this.formatter = new ResultFormatter_1.ResultFormatter(logger);
        this.validator = new ConfigValidator_1.ConfigValidator();
    }
    /**
     * Register the search command with the CLI program
     */
    register(program) {
        program
            .argument('<query>', 'search query')
            .option('-n, --count <number>', 'number of results to return (1-10)', '10')
            .option('-s, --safe <level>', 'safe search level (active|off)', 'active')
            .option('-l, --language <code>', 'language code (e.g., en, es, fr)')
            .option('-c, --country <code>', 'country code (e.g., us, uk, ca)')
            .option('-f, --format <type>', 'output format (json|table|compact)', 'table')
            .option('--no-color', 'disable colored output')
            .description('search Google and display results in the terminal')
            .action(async (query, options) => {
            await this.execute(query, options);
        });
        // Add examples to help
        program.addHelpText('after', `
Examples:
  $ google-cli "TypeScript tutorial"
  $ google-cli "Node.js best practices" --count 5
  $ google-cli "React hooks" --format compact --no-color
  $ google-cli "Python machine learning" --safe off --language en
  $ google-cli "Vue.js documentation" --country us --format json
`);
    }
    /**
     * Execute the search command
     */
    async execute(query, options) {
        try {
            // Validate configuration
            const configValidation = this.validator.validateConfig();
            if (!configValidation.isValid) {
                throw new types_1.GoogleCliError(configValidation.message, types_1.ErrorType.AUTHENTICATION_ERROR);
            }
            // Parse and validate options
            const searchOptions = this.parseOptions(query, options);
            this.validateSearchOptions(searchOptions);
            this.logger.debug('Search options:', searchOptions);
            this.logger.info(`Searching for: ${chalk_1.default.cyan(query)}`);
            // Perform the search
            const results = await this.searchService.search(searchOptions);
            if (!results || results.length === 0) {
                this.logger.warn('No results found for your query.');
                this.logger.info('Try using different keywords or removing filters.');
                return;
            }
            // Format and display results
            await this.formatter.formatResults(results, {
                format: searchOptions.format,
                noColor: searchOptions.noColor,
                maxSnippetLength: 150,
                showNumbers: true,
                compact: searchOptions.format === 'compact'
            });
            this.logger.success(`Found ${results.length} result(s)`);
        }
        catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            this.errorHandler.handleError(err, 'Search');
            process.exit(1);
        }
    }
    /**
     * Parse command line options into SearchOptions
     */
    parseOptions(query, options) {
        const count = parseInt(options.count, 10);
        return {
            query: query.trim(),
            count: isNaN(count) ? 10 : Math.min(Math.max(count, 1), 10),
            safe: options.safe === 'off' ? 'off' : 'active',
            language: options.language,
            country: options.country,
            format: options.format || 'table',
            noColor: options.noColor || false,
            debug: options.debug || false
        };
    }
    /**
     * Validate search options
     */
    validateSearchOptions(options) {
        // Validate query
        if (!options.query || options.query.length === 0) {
            throw new types_1.GoogleCliError('Search query cannot be empty', types_1.ErrorType.VALIDATION_ERROR);
        }
        if (options.query.length > 2048) {
            throw new types_1.GoogleCliError('Search query is too long (maximum 2048 characters)', types_1.ErrorType.VALIDATION_ERROR);
        }
        // Validate count
        if (options.count && (options.count < 1 || options.count > 10)) {
            throw new types_1.GoogleCliError('Count must be between 1 and 10', types_1.ErrorType.VALIDATION_ERROR);
        }
        // Validate safe search
        if (options.safe && !['active', 'off'].includes(options.safe)) {
            throw new types_1.GoogleCliError('Safe search level must be "active" or "off"', types_1.ErrorType.VALIDATION_ERROR);
        }
        // Validate format
        if (options.format && !['json', 'table', 'compact'].includes(options.format)) {
            throw new types_1.GoogleCliError('Format must be "json", "table", or "compact"', types_1.ErrorType.VALIDATION_ERROR);
        }
        // Validate language code (basic check)
        if (options.language && !/^[a-z]{2}(-[A-Z]{2})?$/.test(options.language)) {
            this.logger.warn(`Language code "${options.language}" may not be valid. Expected format: "en" or "en-US"`);
        }
        // Validate country code (basic check)
        if (options.country && !/^[a-z]{2}$/i.test(options.country)) {
            this.logger.warn(`Country code "${options.country}" may not be valid. Expected format: "us", "uk", "ca"`);
        }
    }
}
exports.SearchCommand = SearchCommand;
//# sourceMappingURL=SearchCommand.js.map