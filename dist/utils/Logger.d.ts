import { LogLevel } from '../types';
/**
 * Logger utility for consistent logging throughout the application
 */
export declare class Logger {
    private debugMode;
    private noColor;
    constructor();
    /**
     * Enable or disable debug mode
     */
    setDebugMode(enabled: boolean): void;
    /**
     * Enable or disable colored output
     */
    setColorMode(enabled: boolean): void;
    /**
     * Log an error message
     */
    error(message: string, error?: Error): void;
    /**
     * Log a warning message
     */
    warn(message: string): void;
    /**
     * Log an info message
     */
    info(message: string): void;
    /**
     * Log a success message
     */
    success(message: string): void;
    /**
     * Log a debug message (only shown in debug mode)
     */
    debug(message: string, data?: unknown): void;
    /**
     * Log a message with custom level
     */
    log(level: LogLevel, message: string, data?: unknown): void;
    /**
     * Create a formatted divider
     */
    divider(char?: string, length?: number): void;
    /**
     * Log with custom styling
     */
    styled(message: string, style?: (text: string) => string): void;
    /**
     * Clear the console (if supported)
     */
    clear(): void;
}
//# sourceMappingURL=Logger.d.ts.map