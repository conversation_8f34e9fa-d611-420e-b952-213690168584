{"version": 3, "file": "ResultFormatter.js", "sourceRoot": "", "sources": ["../../src/utils/ResultFormatter.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAI1B;;GAEG;AACH,MAAa,eAAe;IAG1B,YAAoB,MAAc;QAAd,WAAM,GAAN,MAAM,CAAQ;QAChC,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC;IACpD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,aAAa,CACxB,OAAuB,EACvB,UAAyB,EAAE;QAE3B,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAExC,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YACpB,KAAK,MAAM;gBACT,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;gBAC3B,MAAM;YACR,KAAK,SAAS;gBACZ,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBACpC,MAAM;YACR,KAAK,OAAO,CAAC;YACb;gBACE,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAClC,MAAM;QACV,CAAC;IACH,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,OAAsB;QACzC,OAAO;YACL,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa;YAC1C,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,GAAG;YACjD,WAAW,EAAE,OAAO,CAAC,WAAW,KAAK,KAAK;YAC1C,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,KAAK;YACjC,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,KAAK;YACjC,MAAM,EAAG,OAAe,CAAC,MAAM,IAAI,OAAO;SAC3C,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,OAAuB;QAC1C,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,OAAuB,EAAE,OAAgC;QAC/E,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAChC,MAAM,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;YAC3D,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YAC9D,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YAEzD,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;YAEtB,IAAI,KAAK,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/B,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,6BAA6B;YAChD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,OAAuB,EAAE,OAAgC;QAC7E,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QAEnE,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC,CAAC;QAC1D,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAErB,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAChC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;YAEpD,IAAI,KAAK,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YACvE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,kBAAkB,CACxB,MAAoB,EACpB,MAAc,EACd,OAAgC;QAEhC,0BAA0B;QAC1B,MAAM,SAAS,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;YACrC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5E,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QAE9D,OAAO,CAAC,GAAG,CAAC,GAAG,SAAS,GAAG,KAAK,EAAE,CAAC,CAAC;QAEpC,MAAM;QACN,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAEjB,UAAU;QACV,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC5D,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,+BAA+B;IAClD,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,KAAa,EAAE,OAAgB;QACjD,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;QAClE,OAAO,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACK,SAAS,CAAC,GAAW,EAAE,OAAgB;QAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QAC5C,MAAM,YAAY,GAAG,GAAG,CAAC,MAAM,GAAG,YAAY,CAAC,CAAC;YAC9C,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;QAEnD,OAAO,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,eAAK,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,OAAe,EAAE,OAAgC;QACrE,IAAI,gBAAgB,GAAG,OAAO,CAAC;QAE/B,uBAAuB;QACvB,IAAI,gBAAgB,CAAC,MAAM,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;YACvD,gBAAgB,GAAG,GAAG,gBAAgB,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC,KAAK,CAAC;QACzF,CAAC;QAED,8BAA8B;QAC9B,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAE1E,kBAAkB;QAClB,MAAM,eAAe,GAAG,cAAc;aACnC,KAAK,CAAC,IAAI,CAAC;aACX,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC;aACxB,IAAI,CAAC,IAAI,CAAC,CAAC;QAEd,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,IAAY,EAAE,OAAgC;QACjE,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;QAC1D,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,eAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,KAAa,EAAE,OAAgB,EAAE,OAAe,GAAG;QACvE,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAChC,OAAO,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,IAAY,EAAE,KAAa;QAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnE,OAAO,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;IACpC,CAAC;IAED;;OAEG;IACK,QAAQ,CAAC,IAAY,EAAE,KAAa;QAC1C,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,IAAI,WAAW,GAAG,EAAE,CAAC;QAErB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YAE/D,IAAI,QAAQ,CAAC,MAAM,IAAI,KAAK,EAAE,CAAC;gBAC7B,WAAW,GAAG,QAAQ,CAAC;YACzB,CAAC;iBAAM,CAAC;gBACN,IAAI,WAAW,EAAE,CAAC;oBAChB,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBACxB,WAAW,GAAG,IAAI,CAAC;gBACrB,CAAC;qBAAM,CAAC;oBACN,yCAAyC;oBACzC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;oBACrC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtC,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,WAAW,EAAE,CAAC;YAChB,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC1B,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,IAAY,EAAE,WAAqB,EAAE,OAAgB;QAChF,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,eAAe,GAAG,IAAI,CAAC;QAE3B,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACzB,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YAC9D,eAAe,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,eAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;QAEH,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,IAAY;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,oBAAoB,CACzB,KAAa,EACb,KAAa,EACb,UAAmB,EACnB,UAAmB,KAAK;QAExB,MAAM,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,OAAO,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QAClE,MAAM,OAAO,GAAG,SAAS,KAAK,mBAAmB,KAAK,IAAI,OAAO,EAAE,CAAC;QAEpE,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,OAAe,EAAE,UAAmB,KAAK;QAC1D,OAAO,OAAO,CAAC,CAAC,CAAC,UAAU,OAAO,EAAE,CAAC,CAAC,CAAC,eAAK,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,OAAO,EAAE,CAAC,CAAC;IAC7E,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,OAAe,EAAE,UAAmB,KAAK;QAC5D,OAAO,OAAO,CAAC,CAAC,CAAC,YAAY,OAAO,EAAE,CAAC,CAAC,CAAC,eAAK,CAAC,MAAM,CAAC,YAAY,OAAO,EAAE,CAAC,CAAC;IAC/E,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,OAAe,EAAE,UAAmB,KAAK;QACzD,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACjD,CAAC;CACF;AAxRD,0CAwRC"}