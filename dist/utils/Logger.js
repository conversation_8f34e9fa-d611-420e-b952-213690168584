"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logger = void 0;
const chalk_1 = __importDefault(require("chalk"));
const types_1 = require("../types");
/**
 * Logger utility for consistent logging throughout the application
 */
class Logger {
    constructor() {
        this.debugMode = false;
        this.noColor = false;
        this.noColor = process.env.NO_COLOR === '1' || process.env.NODE_ENV === 'test';
    }
    /**
     * Enable or disable debug mode
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
    }
    /**
     * Enable or disable colored output
     */
    setColorMode(enabled) {
        this.noColor = !enabled;
    }
    /**
     * Log an error message
     */
    error(message, error) {
        const prefix = this.noColor ? '[ERROR]' : chalk_1.default.red.bold('[ERROR]');
        console.error(`${prefix} ${message}`);
        if (error && this.debugMode) {
            console.error(this.noColor ? error.stack : chalk_1.default.red(error.stack || error.message));
        }
    }
    /**
     * Log a warning message
     */
    warn(message) {
        const prefix = this.noColor ? '[WARN]' : chalk_1.default.yellow.bold('[WARN]');
        console.warn(`${prefix} ${message}`);
    }
    /**
     * Log an info message
     */
    info(message) {
        const prefix = this.noColor ? '[INFO]' : chalk_1.default.blue.bold('[INFO]');
        console.log(`${prefix} ${message}`);
    }
    /**
     * Log a success message
     */
    success(message) {
        const prefix = this.noColor ? '[SUCCESS]' : chalk_1.default.green.bold('[SUCCESS]');
        console.log(`${prefix} ${message}`);
    }
    /**
     * Log a debug message (only shown in debug mode)
     */
    debug(message, data) {
        if (!this.debugMode) {
            return;
        }
        const prefix = this.noColor ? '[DEBUG]' : chalk_1.default.gray.bold('[DEBUG]');
        console.log(`${prefix} ${message}`);
        if (data !== undefined) {
            console.log(this.noColor ? JSON.stringify(data, null, 2) : chalk_1.default.gray(JSON.stringify(data, null, 2)));
        }
    }
    /**
     * Log a message with custom level
     */
    log(level, message, data) {
        switch (level) {
            case types_1.LogLevel.ERROR:
                this.error(message, data instanceof Error ? data : undefined);
                break;
            case types_1.LogLevel.WARN:
                this.warn(message);
                break;
            case types_1.LogLevel.INFO:
                this.info(message);
                break;
            case types_1.LogLevel.DEBUG:
                this.debug(message, data);
                break;
            default:
                console.log(message);
        }
    }
    /**
     * Create a formatted divider
     */
    divider(char = '-', length = 50) {
        const line = char.repeat(length);
        console.log(this.noColor ? line : chalk_1.default.gray(line));
    }
    /**
     * Log with custom styling
     */
    styled(message, style) {
        if (this.noColor || !style) {
            console.log(message);
        }
        else {
            console.log(style(message));
        }
    }
    /**
     * Clear the console (if supported)
     */
    clear() {
        if (process.stdout.isTTY) {
            console.clear();
        }
    }
}
exports.Logger = Logger;
//# sourceMappingURL=Logger.js.map