import { Command } from 'commander';
import { Logger } from '../utils/Logger';
import { <PERSON><PERSON>rHand<PERSON> } from '../utils/ErrorHandler';
/**
 * Search command implementation
 */
export declare class SearchCommand {
    private logger;
    private errorHandler;
    private searchService;
    private formatter;
    private validator;
    constructor(logger: <PERSON><PERSON>, errorHandler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>);
    /**
     * Register the search command with the CLI program
     */
    register(program: Command): void;
    /**
     * Execute the search command
     */
    private execute;
    /**
     * Parse command line options into SearchOptions
     */
    private parseOptions;
    /**
     * Validate search options
     */
    private validateSearchOptions;
}
//# sourceMappingURL=SearchCommand.d.ts.map