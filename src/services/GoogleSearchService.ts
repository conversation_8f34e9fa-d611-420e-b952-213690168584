import axios, { AxiosInstance, AxiosError } from 'axios';
import { 
  SearchOptions, 
  SearchResult, 
  GoogleSearchResponse, 
  GoogleSearchConfig,
  GoogleCliError,
  ErrorType 
} from '../types';
import { Logger } from '../utils/Logger';
import { <PERSON><PERSON>r<PERSON>and<PERSON> } from '../utils/ErrorHandler';
import { ConfigValidator } from '../utils/ConfigValidator';
import { RateLimiter } from '../utils/RateLimiter';

/**
 * Service for interacting with Google Custom Search API
 */
export class GoogleSearchService {
  private config: GoogleSearchConfig;
  private httpClient: AxiosInstance;
  private rateLimiter: RateLimiter;

  constructor(
    private logger: Logger,
    private errorHandler: ErrorHandler
  ) {
    const validator = new ConfigValidator();
    this.config = validator.getConfig();
    this.rateLimiter = new RateLimiter({
      requestsPerSecond: 10, // Google allows 100 requests per 100 seconds
      requestsPerDay: 100,   // Free tier daily limit
      burstLimit: 5
    });
    
    this.httpClient = this.createHttpClient();
  }

  /**
   * Perform a Google search
   */
  public async search(options: SearchOptions): Promise<SearchResult[]> {
    try {
      // Check rate limits
      await this.rateLimiter.checkLimit();

      this.logger.debug('Performing Google search', { query: options.query });

      // Build search parameters
      const params = this.buildSearchParams(options);
      
      // Make the API request with retries
      const response = await this.makeRequestWithRetries(params);
      
      // Parse and return results
      const results = this.parseSearchResults(response.data);
      
      this.logger.debug(`Found ${results.length} results`);
      return results;

    } catch (error) {
      throw this.handleSearchError(error);
    }
  }

  /**
   * Create and configure the HTTP client
   */
  private createHttpClient(): AxiosInstance {
    const client = axios.create({
      baseURL: this.config.baseUrl || 'https://www.googleapis.com/customsearch/v1',
      timeout: this.config.timeout || 10000,
      headers: {
        'User-Agent': 'google-cli/1.0.0',
        'Accept': 'application/json',
        'Accept-Encoding': 'gzip, deflate'
      }
    });

    // Request interceptor for logging
    client.interceptors.request.use(
      (config) => {
        this.logger.debug('Making API request', {
          url: config.url,
          params: config.params
        });
        return config;
      },
      (error) => {
        this.logger.debug('Request interceptor error', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for logging
    client.interceptors.response.use(
      (response) => {
        this.logger.debug('API response received', {
          status: response.status,
          statusText: response.statusText
        });
        return response;
      },
      (error) => {
        this.logger.debug('Response interceptor error', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          message: error.message
        });
        return Promise.reject(error);
      }
    );

    return client;
  }

  /**
   * Build search parameters for the API request
   */
  private buildSearchParams(options: SearchOptions): Record<string, string> {
    const params: Record<string, string> = {
      key: this.config.apiKey,
      cx: this.config.searchEngineId,
      q: options.query,
      num: (options.count || 10).toString(),
      safe: options.safe || 'active'
    };

    // Add optional parameters
    if (options.language) {
      params['lr'] = `lang_${options.language}`;
    }

    if (options.country) {
      params['gl'] = options.country;
    }

    return params;
  }

  /**
   * Make API request with retry logic
   */
  private async makeRequestWithRetries(
    params: Record<string, string>,
    retryCount: number = 0
  ): Promise<any> {
    const maxRetries = this.config.retries || 3;

    try {
      const response = await this.httpClient.get('', { params });
      return response;
    } catch (error) {
      const axiosError = error as AxiosError;
      
      // Check if we should retry
      if (retryCount < maxRetries && this.shouldRetry(axiosError)) {
        const delay = this.calculateRetryDelay(retryCount);
        this.logger.debug(`Retrying request in ${delay}ms (attempt ${retryCount + 1}/${maxRetries})`);
        
        await this.sleep(delay);
        return this.makeRequestWithRetries(params, retryCount + 1);
      }

      throw error;
    }
  }

  /**
   * Determine if a request should be retried
   */
  private shouldRetry(error: AxiosError): boolean {
    if (!error.response) {
      // Network errors should be retried
      return true;
    }

    const status = error.response.status;
    
    // Retry on server errors and rate limits
    return status >= 500 || status === 429;
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  private calculateRetryDelay(retryCount: number): number {
    const baseDelay = 1000; // 1 second
    const maxDelay = 10000; // 10 seconds
    
    const delay = Math.min(baseDelay * Math.pow(2, retryCount), maxDelay);
    
    // Add jitter to prevent thundering herd
    return delay + Math.random() * 1000;
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Parse Google Search API response into SearchResult array
   */
  private parseSearchResults(data: GoogleSearchResponse): SearchResult[] {
    if (!data.items || data.items.length === 0) {
      return [];
    }

    return data.items.map(item => ({
      title: this.cleanText(item.title),
      link: item.link,
      snippet: this.cleanText(item.snippet),
      displayLink: item.displayLink,
      formattedUrl: item.formattedUrl
    }));
  }

  /**
   * Clean and sanitize text content
   */
  private cleanText(text: string): string {
    if (!text) return '';
    
    return text
      .replace(/\n/g, ' ')           // Replace newlines with spaces
      .replace(/\s+/g, ' ')          // Collapse multiple spaces
      .replace(/&quot;/g, '"')       // Replace HTML entities
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&#39;/g, "'")
      .trim();
  }

  /**
   * Handle search errors and convert to appropriate GoogleCliError
   */
  private handleSearchError(error: unknown): GoogleCliError {
    if (error instanceof GoogleCliError) {
      return error;
    }

    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;
      
      if (!axiosError.response) {
        // Network error
        return new GoogleCliError(
          'Unable to connect to Google Search API. Please check your internet connection.',
          ErrorType.NETWORK_ERROR,
          undefined,
          axiosError
        );
      }

      const status = axiosError.response.status;
      const responseData = axiosError.response.data as any;
      
      switch (status) {
        case 400:
          return new GoogleCliError(
            `Invalid request: ${responseData?.error?.message || 'Bad request'}`,
            ErrorType.VALIDATION_ERROR,
            status,
            responseData
          );
          
        case 401:
        case 403:
          const isQuotaError = responseData?.error?.message?.includes('quota') ||
                              responseData?.error?.message?.includes('limit');
          
          if (isQuotaError) {
            return new GoogleCliError(
              'API quota exceeded. Please check your usage limits.',
              ErrorType.RATE_LIMIT_ERROR,
              status,
              responseData
            );
          }
          
          return new GoogleCliError(
            'Authentication failed. Please check your API key and search engine ID.',
            ErrorType.AUTHENTICATION_ERROR,
            status,
            responseData
          );
          
        case 429:
          return new GoogleCliError(
            'Rate limit exceeded. Please wait before making another request.',
            ErrorType.RATE_LIMIT_ERROR,
            status,
            responseData
          );
          
        case 500:
        case 502:
        case 503:
        case 504:
          return new GoogleCliError(
            'Google Search API is temporarily unavailable. Please try again later.',
            ErrorType.API_ERROR,
            status,
            responseData
          );
          
        default:
          return new GoogleCliError(
            `API error: ${responseData?.error?.message || 'Unknown error'}`,
            ErrorType.API_ERROR,
            status,
            responseData
          );
      }
    }

    // Generic error
    const message = error instanceof Error ? error.message : String(error);
    return new GoogleCliError(
      `Search failed: ${message}`,
      ErrorType.UNKNOWN_ERROR,
      undefined,
      error
    );
  }
}
