{"version": 3, "file": "EdgeCaseHandler.js", "sourceRoot": "", "sources": ["../../src/utils/EdgeCaseHandler.ts"], "names": [], "mappings": ";;;AAAA,oCAAqD;AAGrD;;GAEG;AACH,MAAa,eAAe;IAC1B,YAAoB,MAAc;QAAd,WAAM,GAAN,MAAM,CAAQ;IAAG,CAAC;IAEtC;;OAEG;IACI,mBAAmB,CAAC,KAAa;QACtC,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YACxC,MAAM,IAAI,sBAAc,CACtB,yCAAyC,EACzC,iBAAS,CAAC,gBAAgB,CAC3B,CAAC;QACJ,CAAC;QAED,kBAAkB;QAClB,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QAE7B,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,MAAM,IAAI,sBAAc,CACtB,yDAAyD,EACzD,iBAAS,CAAC,gBAAgB,CAC3B,CAAC;QACJ,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;YAC1B,MAAM,IAAI,sBAAc,CACtB,oDAAoD,EACpD,iBAAS,CAAC,gBAAgB,CAC3B,CAAC;QACJ,CAAC;QAED,+CAA+C;QAC/C,MAAM,gBAAgB,GAAG,aAAa,CAAC;QACvC,IAAI,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;QACvF,CAAC;QAED,iBAAiB;QACjB,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,KAAa;QACjC,OAAO,KAAK;aACT,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAW,uBAAuB;aACtD,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAAI,2DAA2D;aAC1F,IAAI,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,wBAAwB;QACnC,IAAI,CAAC;YACH,iDAAiD;YACjD,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;YAC3B,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAErC,MAAM,MAAM,CAAC,YAAY,CAAC,CAAC;YAC3B,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC9D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACI,mBAAmB;QACxB,wBAAwB;QACxB,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC;QACpC,MAAM,YAAY,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC,CAAC;QAE7E,IAAI,YAAY,GAAG,EAAE,EAAE,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,WAAW,sEAAsE,CAAC,CAAC;QACzH,CAAC;QAED,6BAA6B;QAC7B,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,8BAA8B;QAC9B,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,MAAM,eAAe,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;QAExD,KAAK,MAAM,MAAM,IAAI,eAAe,EAAE,CAAC;YACrC,IAAI,CAAC;gBACH,OAAO,CAAC,MAAM,CAAC,CAAC;YAClB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,IAAI,sBAAc,CACtB,oBAAoB,MAAM,uEAAuE,EACjG,iBAAS,CAAC,gBAAgB,CAC3B,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,yBAAyB;QAC/B,4BAA4B;QAC5B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACxD,CAAC;QAED,uBAAuB;QACvB,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC;QACrC,IAAI,CAAC,KAAK,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;YACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;QACvF,CAAC;QAED,sBAAsB;QACtB,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;YAC5D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,KAAU;QACnC,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC;QAEpC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9B,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC9B,OAAO,IAAI,sBAAc,CACvB,2EAA2E,EAC3E,iBAAS,CAAC,gBAAgB,EAC1B,KAAK,CAAC,UAAU,EAChB,KAAK,CACN,CAAC;YACJ,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBACxC,OAAO,IAAI,sBAAc,CACvB,oGAAoG,EACpG,iBAAS,CAAC,gBAAgB,EAC1B,KAAK,CAAC,UAAU,EAChB,KAAK,CACN,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,IAAI,sBAAc,CACvB,gFAAgF,EAChF,iBAAS,CAAC,gBAAgB,EAC1B,KAAK,CAAC,UAAU,EAChB,KAAK,CACN,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,QAAa;QACtC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,sBAAc,CACtB,yCAAyC,EACzC,iBAAS,CAAC,SAAS,CACpB,CAAC;QACJ,CAAC;QAED,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACjC,MAAM,IAAI,sBAAc,CACtB,kDAAkD,EAClD,iBAAS,CAAC,SAAS,CACpB,CAAC;QACJ,CAAC;QAED,kCAAkC;QAClC,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;YACnB,MAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,IAAI,mBAAmB,CAAC;YACnE,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC;YAEnD,MAAM,IAAI,sBAAc,CACtB,qBAAqB,SAAS,MAAM,YAAY,EAAE,EAClD,iBAAS,CAAC,SAAS,EACnB,QAAQ,CAAC,KAAK,CAAC,IAAI,CACpB,CAAC;QACJ,CAAC;QAED,8BAA8B;QAC9B,IAAI,CAAC,QAAQ,CAAC,iBAAiB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YACnD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,OAAe;QACvC,OAAO,IAAI,sBAAc,CACvB,2BAA2B,OAAO,wDAAwD,EAC1F,iBAAS,CAAC,aAAa,CACxB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,KAAU;QAC9B,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC;QAEpC,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1F,OAAO,IAAI,sBAAc,CACvB,yGAAyG,EACzG,iBAAS,CAAC,aAAa,EACvB,SAAS,EACT,KAAK,CACN,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,sBAAc,CACvB,iCAAiC,EACjC,iBAAS,CAAC,aAAa,EACvB,SAAS,EACT,KAAK,CACN,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,KAAU;QAChC,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC;QAEpC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACnG,OAAO,IAAI,sBAAc,CACvB,4HAA4H,EAC5H,iBAAS,CAAC,aAAa,EACvB,SAAS,EACT,KAAK,CACN,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,sBAAc,CACvB,mCAAmC,EACnC,iBAAS,CAAC,aAAa,EACvB,SAAS,EACT,KAAK,CACN,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,sBAAsB,CAAC,KAAqB;QACjD,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,iBAAS,CAAC,aAAa;gBAC1B,WAAW,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;gBACnD,WAAW,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;gBAC/C,WAAW,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;gBAChE,MAAM;YAER,KAAK,iBAAS,CAAC,SAAS;gBACtB,WAAW,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;gBACnD,WAAW,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;gBAC9D,WAAW,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;gBAC1D,MAAM;YAER,KAAK,iBAAS,CAAC,gBAAgB;gBAC7B,WAAW,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;gBACvD,WAAW,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;gBACtD,WAAW,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;gBACrD,MAAM;YAER,KAAK,iBAAS,CAAC,oBAAoB;gBACjC,WAAW,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;gBACnE,WAAW,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;gBAC5E,WAAW,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;gBACtE,MAAM;YAER,KAAK,iBAAS,CAAC,gBAAgB;gBAC7B,WAAW,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;gBACjD,WAAW,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;gBACtD,WAAW,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;gBAClD,MAAM;YAER;gBACE,WAAW,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;gBAC1C,WAAW,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;gBAC5D,MAAM;QACV,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,KAAqB;QAC7C,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,iBAAS,CAAC,aAAa,CAAC;YAC7B,KAAK,iBAAS,CAAC,gBAAgB;gBAC7B,OAAO,IAAI,CAAC;YAEd,KAAK,iBAAS,CAAC,SAAS;gBACtB,qDAAqD;gBACrD,OAAO,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;YAE5D;gBACE,OAAO,KAAK,CAAC;QACjB,CAAC;IACH,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,OAAe,EAAE,KAAqB;QAC/D,MAAM,SAAS,GAAG,IAAI,CAAC,CAAC,WAAW;QACnC,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,aAAa;QAErC,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAS,CAAC,gBAAgB,EAAE,CAAC;YAC9C,sCAAsC;YACtC,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC;QAClE,CAAC;QAED,+BAA+B;QAC/B,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAC;IAC9D,CAAC;CACF;AA5UD,0CA4UC"}