import { GoogleCliError, ErrorType } from '../types';
import { Logger } from './Logger';
/**
 * Centralized error handling for the CLI application
 */
export declare class ErrorHandler {
    private logger;
    constructor(logger: Logger);
    /**
     * Handle different types of errors with appropriate messaging
     */
    handleError(error: Error | GoogleCliError, context?: string): void;
    /**
     * Handle Google CLI specific errors
     */
    private handleGoogleCliError;
    /**
     * Handle generic JavaScript errors
     */
    private handleGenericError;
    /**
     * Create a GoogleCliError from a generic error
     */
    createError(message: string, type?: ErrorType, originalError?: Error, statusCode?: number): GoogleCliError;
    /**
     * Wrap an async function with error handling
     */
    withErrorHandling<T>(fn: () => Promise<T>, context?: string): Promise<T | null>;
    /**
     * Check if error is recoverable
     */
    isRecoverableError(error: Error | GoogleCliError): boolean;
    /**
     * Get user-friendly error message
     */
    getUserFriendlyMessage(error: Error | GoogleCliError): string;
}
//# sourceMappingURL=ErrorHandler.d.ts.map