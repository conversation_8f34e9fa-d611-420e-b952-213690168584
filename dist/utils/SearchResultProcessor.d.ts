import { SearchResult } from '../types';
/**
 * Processes and enhances search results
 */
export declare class SearchResultProcessor {
    /**
     * Process raw search results with filtering and enhancement
     */
    processResults(results: SearchResult[], options?: {
        removeDuplicates?: boolean;
        filterSpam?: boolean;
        enhanceSnippets?: boolean;
        sortByRelevance?: boolean;
        maxResults?: number;
    }): SearchResult[];
    /**
     * Remove duplicate results based on URL and title similarity
     */
    private removeDuplicates;
    /**
     * Generate a unique key for a search result
     */
    private generateResultKey;
    /**
     * Normalize URL for comparison
     */
    private normalizeUrl;
    /**
     * Normalize title for comparison
     */
    private normalizeTitle;
    /**
     * Filter out spam and low-quality results
     */
    private filterSpamResults;
    /**
     * Check if title contains spam indicators
     */
    private isSpamTitle;
    /**
     * Check if URL contains spam indicators
     */
    private isSpamUrl;
    /**
     * Check if snippet contains spam indicators
     */
    private isSpamSnippet;
    /**
     * Enhance snippets with better formatting and information
     */
    private enhanceSnippets;
    /**
     * Enhance a single snippet
     */
    private enhanceSnippet;
    /**
     * Sort results by relevance score
     */
    private sortByRelevance;
    /**
     * Calculate relevance score for a result
     */
    private calculateRelevanceScore;
    /**
     * Get domain authority score (simplified)
     */
    private getDomainAuthorityScore;
    /**
     * Get title quality score
     */
    private getTitleQualityScore;
    /**
     * Get snippet quality score
     */
    private getSnippetQualityScore;
    /**
     * Get URL quality score
     */
    private getUrlQualityScore;
}
//# sourceMappingURL=SearchResultProcessor.d.ts.map