import { spawn } from 'child_process';
import { join } from 'path';

describe('CLI Integration Tests', () => {
  const cliPath = join(__dirname, '../index.ts');
  const timeout = 30000; // 30 seconds

  // Helper function to run CLI command
  const runCLI = (args: string[]): Promise<{ stdout: string; stderr: string; exitCode: number }> => {
    return new Promise((resolve) => {
      const child = spawn('npx', ['ts-node', cliPath, ...args], {
        env: {
          ...process.env,
          NODE_ENV: 'test',
          GOOGLE_API_KEY: 'test-api-key',
          GOOGLE_SEARCH_ENGINE_ID: 'test-search-engine-id'
        }
      });

      let stdout = '';
      let stderr = '';

      child.stdout?.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr?.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        resolve({
          stdout,
          stderr,
          exitCode: code || 0
        });
      });

      // Kill process after timeout
      setTimeout(() => {
        child.kill();
        resolve({
          stdout,
          stderr,
          exitCode: 1
        });
      }, timeout);
    });
  };

  describe('Help and Version', () => {
    it('should display help when no arguments provided', async () => {
      const result = await runCLI([]);

      expect(result.stdout).toContain('Usage:');
      expect(result.stdout).toContain('google-cli');
      expect(result.stdout).toContain('search Google and display results');
    });

    it('should display help with --help flag', async () => {
      const result = await runCLI(['--help']);

      expect(result.stdout).toContain('Usage:');
      expect(result.stdout).toContain('Examples:');
      expect(result.exitCode).toBe(0);
    });

    it('should display version with --version flag', async () => {
      const result = await runCLI(['--version']);

      expect(result.stdout).toContain('1.0.0');
      expect(result.exitCode).toBe(0);
    });
  });

  describe('Configuration Validation', () => {
    it('should show error for missing API key', async () => {
      const child = spawn('npx', ['ts-node', cliPath, 'test query'], {
        env: {
          ...process.env,
          NODE_ENV: 'production', // Don't use test environment
          GOOGLE_API_KEY: '', // Empty API key
          GOOGLE_SEARCH_ENGINE_ID: 'test-search-engine-id'
        }
      });

      let stderr = '';
      child.stderr?.on('data', (data) => {
        stderr += data.toString();
      });

      await new Promise((resolve) => {
        child.on('close', resolve);
      });

      expect(stderr).toContain('Missing required configuration');
      expect(stderr).toContain('GOOGLE_API_KEY');
    });

    it('should show error for missing search engine ID', async () => {
      const child = spawn('npx', ['ts-node', cliPath, 'test query'], {
        env: {
          ...process.env,
          NODE_ENV: 'production',
          GOOGLE_API_KEY: 'test-api-key',
          GOOGLE_SEARCH_ENGINE_ID: '' // Empty search engine ID
        }
      });

      let stderr = '';
      child.stderr?.on('data', (data) => {
        stderr += data.toString();
      });

      await new Promise((resolve) => {
        child.on('close', resolve);
      });

      expect(stderr).toContain('Missing required configuration');
      expect(stderr).toContain('GOOGLE_SEARCH_ENGINE_ID');
    });
  });

  describe('Search Command Options', () => {
    it('should accept count option', async () => {
      const result = await runCLI(['test query', '--count', '5']);

      // In test environment, this should not make actual API calls
      // but should validate the options
      expect(result.exitCode).toBe(0);
    });

    it('should accept safe search option', async () => {
      const result = await runCLI(['test query', '--safe', 'off']);

      expect(result.exitCode).toBe(0);
    });

    it('should accept language option', async () => {
      const result = await runCLI(['test query', '--language', 'en']);

      expect(result.exitCode).toBe(0);
    });

    it('should accept country option', async () => {
      const result = await runCLI(['test query', '--country', 'us']);

      expect(result.exitCode).toBe(0);
    });

    it('should accept format option', async () => {
      const result = await runCLI(['test query', '--format', 'json']);

      expect(result.exitCode).toBe(0);
    });

    it('should accept no-color option', async () => {
      const result = await runCLI(['test query', '--no-color']);

      expect(result.exitCode).toBe(0);
    });

    it('should accept debug option', async () => {
      const result = await runCLI(['test query', '--debug']);

      expect(result.exitCode).toBe(0);
    });
  });

  describe('Input Validation', () => {
    it('should reject empty query', async () => {
      const result = await runCLI(['']);

      expect(result.stderr).toContain('Search query cannot be empty');
      expect(result.exitCode).toBe(1);
    });

    it('should reject invalid count', async () => {
      const result = await runCLI(['test query', '--count', '15']);

      expect(result.stderr).toContain('Count must be between 1 and 10');
      expect(result.exitCode).toBe(1);
    });

    it('should reject invalid safe search level', async () => {
      const result = await runCLI(['test query', '--safe', 'invalid']);

      expect(result.stderr).toContain('Safe search level must be');
      expect(result.exitCode).toBe(1);
    });

    it('should reject invalid format', async () => {
      const result = await runCLI(['test query', '--format', 'invalid']);

      expect(result.stderr).toContain('Format must be');
      expect(result.exitCode).toBe(1);
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      // This test would require mocking network failures
      // For now, we'll test that the CLI doesn't crash on invalid input
      const result = await runCLI(['test query with special chars !@#$%']);

      // Should not crash, even if it fails
      expect(typeof result.exitCode).toBe('number');
    });

    it('should handle very long queries', async () => {
      const longQuery = 'a'.repeat(3000);
      const result = await runCLI([longQuery]);

      expect(result.stderr).toContain('Search query is too long');
      expect(result.exitCode).toBe(1);
    });
  });

  describe('Output Formats', () => {
    it('should support JSON output format', async () => {
      const result = await runCLI(['test query', '--format', 'json']);

      // In test environment, should not make actual API calls
      // but should accept the format option
      expect(result.exitCode).toBe(0);
    });

    it('should support table output format', async () => {
      const result = await runCLI(['test query', '--format', 'table']);

      expect(result.exitCode).toBe(0);
    });

    it('should support compact output format', async () => {
      const result = await runCLI(['test query', '--format', 'compact']);

      expect(result.exitCode).toBe(0);
    });
  });

  describe('Global Options', () => {
    it('should enable debug mode', async () => {
      const result = await runCLI(['test query', '--debug']);

      // Debug mode should be enabled without errors
      expect(result.exitCode).toBe(0);
    });

    it('should disable colors', async () => {
      const result = await runCLI(['test query', '--no-color']);

      expect(result.exitCode).toBe(0);
    });
  });

  describe('Signal Handling', () => {
    it('should handle SIGINT gracefully', async () => {
      const child = spawn('npx', ['ts-node', cliPath, 'test query'], {
        env: {
          ...process.env,
          NODE_ENV: 'test',
          GOOGLE_API_KEY: 'test-api-key',
          GOOGLE_SEARCH_ENGINE_ID: 'test-search-engine-id'
        }
      });

      // Send SIGINT after a short delay
      setTimeout(() => {
        child.kill('SIGINT');
      }, 100);

      const exitCode = await new Promise<number>((resolve) => {
        child.on('close', (code) => {
          resolve(code || 0);
        });
      });

      // Should exit cleanly
      expect(typeof exitCode).toBe('number');
    }, timeout);
  });
});
