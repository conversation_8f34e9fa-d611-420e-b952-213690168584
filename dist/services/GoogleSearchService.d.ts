import { SearchOptions, SearchResult } from '../types';
import { Logger } from '../utils/Logger';
import { <PERSON><PERSON>rHandler } from '../utils/ErrorHandler';
/**
 * Service for interacting with Google Custom Search API
 */
export declare class GoogleSearchService {
    private logger;
    private errorHandler;
    private config;
    private httpClient;
    private rateLimiter;
    constructor(logger: Logger, errorHandler: <PERSON>rrorHandler);
    /**
     * Perform a Google search
     */
    search(options: SearchOptions): Promise<SearchResult[]>;
    /**
     * Create and configure the HTTP client
     */
    private createHttpClient;
    /**
     * Build search parameters for the API request
     */
    private buildSearchParams;
    /**
     * Make API request with retry logic
     */
    private makeRequestWithRetries;
    /**
     * Determine if a request should be retried
     */
    private shouldRetry;
    /**
     * Calculate retry delay with exponential backoff
     */
    private calculateRetryDelay;
    /**
     * Sleep for specified milliseconds
     */
    private sleep;
    /**
     * Parse Google Search API response into SearchResult array
     */
    private parseSearchResults;
    /**
     * Clean and sanitize text content
     */
    private cleanText;
    /**
     * Handle search errors and convert to appropriate GoogleCliError
     */
    private handleSearchError;
}
//# sourceMappingURL=GoogleSearchService.d.ts.map