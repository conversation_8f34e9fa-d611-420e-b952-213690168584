{"name": "google-cli", "version": "1.0.0", "description": "A command-line interface tool for performing Google web searches with rich terminal formatting", "main": "dist/index.js", "bin": {"google-cli": "./dist/index.js"}, "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "start": "node dist/index.js", "prepublishOnly": "npm run build"}, "keywords": ["google", "search", "cli", "terminal", "command-line"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "engines": {"node": ">=16.0.0"}, "files": ["dist", "README.md", "LICENSE"], "dependencies": {"axios": "^1.10.0", "chalk": "^4.1.2", "commander": "^9.5.0", "dotenv": "^17.0.0"}, "devDependencies": {"@types/jest": "^30.0.0", "@types/node": "^24.0.7", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "eslint": "^9.30.0", "jest": "^30.0.3", "nodemon": "^3.1.10", "prettier": "^3.6.2", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}