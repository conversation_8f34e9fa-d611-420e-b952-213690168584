/**
 * Jest test setup file
 */
export declare const testUtils: {
    createMockSearchResult: (overrides?: {}) => {
        title: string;
        link: string;
        snippet: string;
        displayLink: string;
        formattedUrl: string;
    };
    createMockApiResponse: (overrides?: {}) => {
        kind: string;
        url: {
            type: string;
            template: string;
        };
        queries: {
            request: {
                title: string;
                totalResults: string;
                searchTerms: string;
                count: number;
                startIndex: number;
                inputEncoding: string;
                outputEncoding: string;
                safe: string;
                cx: string;
            }[];
        };
        context: {
            title: string;
        };
        searchInformation: {
            searchTime: number;
            formattedSearchTime: string;
            totalResults: string;
            formattedTotalResults: string;
        };
        items: {
            title: string;
            link: string;
            snippet: string;
            displayLink: string;
            formattedUrl: string;
        }[];
    };
};
//# sourceMappingURL=setup.d.ts.map