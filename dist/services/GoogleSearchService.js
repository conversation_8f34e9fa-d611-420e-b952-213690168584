"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoogleSearchService = void 0;
const axios_1 = __importDefault(require("axios"));
const types_1 = require("../types");
const ConfigValidator_1 = require("../utils/ConfigValidator");
const RateLimiter_1 = require("../utils/RateLimiter");
/**
 * Service for interacting with Google Custom Search API
 */
class GoogleSearchService {
    constructor(logger, errorHandler) {
        this.logger = logger;
        this.errorHandler = errorHandler;
        const validator = new ConfigValidator_1.ConfigValidator();
        this.config = validator.getConfig();
        this.rateLimiter = new RateLimiter_1.RateLimiter({
            requestsPerSecond: 10, // Google allows 100 requests per 100 seconds
            requestsPerDay: 100, // Free tier daily limit
            burstLimit: 5
        });
        this.httpClient = this.createHttpClient();
    }
    /**
     * Perform a Google search
     */
    async search(options) {
        try {
            // Check rate limits
            await this.rateLimiter.checkLimit();
            this.logger.debug('Performing Google search', { query: options.query });
            // Build search parameters
            const params = this.buildSearchParams(options);
            // Make the API request with retries
            const response = await this.makeRequestWithRetries(params);
            // Parse and return results
            const results = this.parseSearchResults(response.data);
            this.logger.debug(`Found ${results.length} results`);
            return results;
        }
        catch (error) {
            throw this.handleSearchError(error);
        }
    }
    /**
     * Create and configure the HTTP client
     */
    createHttpClient() {
        const client = axios_1.default.create({
            baseURL: this.config.baseUrl || 'https://www.googleapis.com/customsearch/v1',
            timeout: this.config.timeout || 10000,
            headers: {
                'User-Agent': 'google-cli/1.0.0',
                'Accept': 'application/json',
                'Accept-Encoding': 'gzip, deflate'
            }
        });
        // Request interceptor for logging
        client.interceptors.request.use((config) => {
            this.logger.debug('Making API request', {
                url: config.url,
                params: config.params
            });
            return config;
        }, (error) => {
            this.logger.debug('Request interceptor error', error);
            return Promise.reject(error);
        });
        // Response interceptor for logging
        client.interceptors.response.use((response) => {
            this.logger.debug('API response received', {
                status: response.status,
                statusText: response.statusText
            });
            return response;
        }, (error) => {
            this.logger.debug('Response interceptor error', {
                status: error.response?.status,
                statusText: error.response?.statusText,
                message: error.message
            });
            return Promise.reject(error);
        });
        return client;
    }
    /**
     * Build search parameters for the API request
     */
    buildSearchParams(options) {
        const params = {
            key: this.config.apiKey,
            cx: this.config.searchEngineId,
            q: options.query,
            num: (options.count || 10).toString(),
            safe: options.safe || 'active'
        };
        // Add optional parameters
        if (options.language) {
            params['lr'] = `lang_${options.language}`;
        }
        if (options.country) {
            params['gl'] = options.country;
        }
        return params;
    }
    /**
     * Make API request with retry logic
     */
    async makeRequestWithRetries(params, retryCount = 0) {
        const maxRetries = this.config.retries || 3;
        try {
            const response = await this.httpClient.get('', { params });
            return response;
        }
        catch (error) {
            const axiosError = error;
            // Check if we should retry
            if (retryCount < maxRetries && this.shouldRetry(axiosError)) {
                const delay = this.calculateRetryDelay(retryCount);
                this.logger.debug(`Retrying request in ${delay}ms (attempt ${retryCount + 1}/${maxRetries})`);
                await this.sleep(delay);
                return this.makeRequestWithRetries(params, retryCount + 1);
            }
            throw error;
        }
    }
    /**
     * Determine if a request should be retried
     */
    shouldRetry(error) {
        if (!error.response) {
            // Network errors should be retried
            return true;
        }
        const status = error.response.status;
        // Retry on server errors and rate limits
        return status >= 500 || status === 429;
    }
    /**
     * Calculate retry delay with exponential backoff
     */
    calculateRetryDelay(retryCount) {
        const baseDelay = 1000; // 1 second
        const maxDelay = 10000; // 10 seconds
        const delay = Math.min(baseDelay * Math.pow(2, retryCount), maxDelay);
        // Add jitter to prevent thundering herd
        return delay + Math.random() * 1000;
    }
    /**
     * Sleep for specified milliseconds
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    /**
     * Parse Google Search API response into SearchResult array
     */
    parseSearchResults(data) {
        if (!data.items || data.items.length === 0) {
            return [];
        }
        return data.items.map(item => ({
            title: this.cleanText(item.title),
            link: item.link,
            snippet: this.cleanText(item.snippet),
            displayLink: item.displayLink,
            formattedUrl: item.formattedUrl
        }));
    }
    /**
     * Clean and sanitize text content
     */
    cleanText(text) {
        if (!text)
            return '';
        return text
            .replace(/\n/g, ' ') // Replace newlines with spaces
            .replace(/\s+/g, ' ') // Collapse multiple spaces
            .replace(/&quot;/g, '"') // Replace HTML entities
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&#39;/g, "'")
            .trim();
    }
    /**
     * Handle search errors and convert to appropriate GoogleCliError
     */
    handleSearchError(error) {
        if (error instanceof types_1.GoogleCliError) {
            return error;
        }
        if (axios_1.default.isAxiosError(error)) {
            const axiosError = error;
            if (!axiosError.response) {
                // Network error
                return new types_1.GoogleCliError('Unable to connect to Google Search API. Please check your internet connection.', types_1.ErrorType.NETWORK_ERROR, undefined, axiosError);
            }
            const status = axiosError.response.status;
            const responseData = axiosError.response.data;
            switch (status) {
                case 400:
                    return new types_1.GoogleCliError(`Invalid request: ${responseData?.error?.message || 'Bad request'}`, types_1.ErrorType.VALIDATION_ERROR, status, responseData);
                case 401:
                case 403:
                    const isQuotaError = responseData?.error?.message?.includes('quota') ||
                        responseData?.error?.message?.includes('limit');
                    if (isQuotaError) {
                        return new types_1.GoogleCliError('API quota exceeded. Please check your usage limits.', types_1.ErrorType.RATE_LIMIT_ERROR, status, responseData);
                    }
                    return new types_1.GoogleCliError('Authentication failed. Please check your API key and search engine ID.', types_1.ErrorType.AUTHENTICATION_ERROR, status, responseData);
                case 429:
                    return new types_1.GoogleCliError('Rate limit exceeded. Please wait before making another request.', types_1.ErrorType.RATE_LIMIT_ERROR, status, responseData);
                case 500:
                case 502:
                case 503:
                case 504:
                    return new types_1.GoogleCliError('Google Search API is temporarily unavailable. Please try again later.', types_1.ErrorType.API_ERROR, status, responseData);
                default:
                    return new types_1.GoogleCliError(`API error: ${responseData?.error?.message || 'Unknown error'}`, types_1.ErrorType.API_ERROR, status, responseData);
            }
        }
        // Generic error
        const message = error instanceof Error ? error.message : String(error);
        return new types_1.GoogleCliError(`Search failed: ${message}`, types_1.ErrorType.UNKNOWN_ERROR, undefined, error);
    }
}
exports.GoogleSearchService = GoogleSearchService;
//# sourceMappingURL=GoogleSearchService.js.map