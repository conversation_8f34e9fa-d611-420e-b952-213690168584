"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RateLimiter = void 0;
const types_1 = require("../types");
/**
 * Rate limiter to prevent exceeding API quotas
 */
class RateLimiter {
    constructor(config) {
        this.config = config;
        this.requestTimes = [];
        this.dailyRequestCount = 0;
        this.lastResetDate = '';
        this.resetDailyCountIfNeeded();
    }
    /**
     * Check if a request can be made within rate limits
     */
    async checkLimit() {
        this.resetDailyCountIfNeeded();
        this.cleanOldRequests();
        // Check daily limit
        if (this.dailyRequestCount >= this.config.requestsPerDay) {
            throw new types_1.GoogleCliError(`Daily request limit of ${this.config.requestsPerDay} exceeded. Please try again tomorrow.`, types_1.ErrorType.RATE_LIMIT_ERROR);
        }
        // Check per-second limit
        const now = Date.now();
        const recentRequests = this.requestTimes.filter(time => now - time < 1000);
        if (recentRequests.length >= this.config.requestsPerSecond) {
            const oldestRequest = Math.min(...recentRequests);
            const waitTime = 1000 - (now - oldestRequest);
            if (waitTime > 0) {
                throw new types_1.GoogleCliError(`Rate limit exceeded. Please wait ${Math.ceil(waitTime / 1000)} second(s) before making another request.`, types_1.ErrorType.RATE_LIMIT_ERROR);
            }
        }
        // Check burst limit
        const burstWindow = 10000; // 10 seconds
        const burstRequests = this.requestTimes.filter(time => now - time < burstWindow);
        if (burstRequests.length >= this.config.burstLimit) {
            const oldestBurstRequest = Math.min(...burstRequests);
            const waitTime = burstWindow - (now - oldestBurstRequest);
            if (waitTime > 0) {
                throw new types_1.GoogleCliError(`Burst limit exceeded. Please wait ${Math.ceil(waitTime / 1000)} second(s) before making another request.`, types_1.ErrorType.RATE_LIMIT_ERROR);
            }
        }
        // Record this request
        this.recordRequest();
    }
    /**
     * Record a successful request
     */
    recordRequest() {
        const now = Date.now();
        this.requestTimes.push(now);
        this.dailyRequestCount++;
    }
    /**
     * Clean up old request timestamps
     */
    cleanOldRequests() {
        const now = Date.now();
        const cutoff = now - 60000; // Keep last minute of requests
        this.requestTimes = this.requestTimes.filter(time => time > cutoff);
    }
    /**
     * Reset daily count if it's a new day
     */
    resetDailyCountIfNeeded() {
        const today = new Date().toDateString();
        if (this.lastResetDate !== today) {
            this.dailyRequestCount = 0;
            this.lastResetDate = today;
        }
    }
    /**
     * Get current rate limit status
     */
    getStatus() {
        this.resetDailyCountIfNeeded();
        this.cleanOldRequests();
        const now = Date.now();
        const requestsInLastSecond = this.requestTimes.filter(time => now - time < 1000).length;
        const requestsInBurstWindow = this.requestTimes.filter(time => now - time < 10000).length;
        return {
            dailyRequestsRemaining: Math.max(0, this.config.requestsPerDay - this.dailyRequestCount),
            requestsInLastSecond,
            requestsInBurstWindow
        };
    }
    /**
     * Calculate how long to wait before next request
     */
    getWaitTime() {
        this.resetDailyCountIfNeeded();
        this.cleanOldRequests();
        const now = Date.now();
        // Check daily limit
        if (this.dailyRequestCount >= this.config.requestsPerDay) {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(0, 0, 0, 0);
            return tomorrow.getTime() - now;
        }
        // Check per-second limit
        const recentRequests = this.requestTimes.filter(time => now - time < 1000);
        if (recentRequests.length >= this.config.requestsPerSecond) {
            const oldestRequest = Math.min(...recentRequests);
            const waitTime = 1000 - (now - oldestRequest);
            if (waitTime > 0) {
                return waitTime;
            }
        }
        // Check burst limit
        const burstRequests = this.requestTimes.filter(time => now - time < 10000);
        if (burstRequests.length >= this.config.burstLimit) {
            const oldestBurstRequest = Math.min(...burstRequests);
            const waitTime = 10000 - (now - oldestBurstRequest);
            if (waitTime > 0) {
                return waitTime;
            }
        }
        return 0;
    }
    /**
     * Reset all rate limiting data (useful for testing)
     */
    reset() {
        this.requestTimes = [];
        this.dailyRequestCount = 0;
        this.lastResetDate = '';
    }
}
exports.RateLimiter = RateLimiter;
//# sourceMappingURL=RateLimiter.js.map