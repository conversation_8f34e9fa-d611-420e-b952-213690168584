import { SearchResult, FormatOptions } from '../types';
import { Logger } from './Logger';
/**
 * Formats search results for terminal display
 */
export declare class ResultFormatter {
    private logger;
    private terminalWidth;
    constructor(logger: Logger);
    /**
     * Format and display search results
     */
    formatResults(results: SearchResult[], options?: FormatOptions): Promise<void>;
    /**
     * Merge default options with provided options
     */
    private mergeOptions;
    /**
     * Format results as JSON
     */
    private formatAsJson;
    /**
     * Format results as a compact list
     */
    private formatAsCompact;
    /**
     * Format results as a detailed table
     */
    private formatAsTable;
    /**
     * Format a single search result
     */
    private formatSingleResult;
    /**
     * Format the title with appropriate styling
     */
    private formatTitle;
    /**
     * Format the URL with appropriate styling
     */
    private formatUrl;
    /**
     * Format the snippet with appropriate styling and length
     */
    private formatSnippet;
    /**
     * Format the header
     */
    private formatHeader;
    /**
     * Create a divider line
     */
    private createDivider;
    /**
     * Center text within a given width
     */
    private centerText;
    /**
     * Wrap text to fit within specified width
     */
    private wrapText;
    /**
     * Highlight search terms in text (for future enhancement)
     */
    private highlightSearchTerms;
    /**
     * Escape special regex characters
     */
    private escapeRegex;
    /**
     * Format results count summary
     */
    formatResultsSummary(count: number, query: string, searchTime?: number, noColor?: boolean): string;
    /**
     * Format error message for display
     */
    formatError(message: string, noColor?: boolean): string;
    /**
     * Format warning message for display
     */
    formatWarning(message: string, noColor?: boolean): string;
    /**
     * Format info message for display
     */
    formatInfo(message: string, noColor?: boolean): string;
}
//# sourceMappingURL=ResultFormatter.d.ts.map