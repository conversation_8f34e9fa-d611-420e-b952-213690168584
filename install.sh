#!/bin/bash

# Google CLI Installation Script
# This script installs Google CLI globally on your system

set -e

echo "🔍 Google CLI Installation Script"
echo "================================="
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 16.0.0 or higher first."
    echo "   Visit: https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2)
REQUIRED_VERSION="16.0.0"

if ! node -e "process.exit(process.version.slice(1).split('.').map(Number).reduce((a,b,i)=>(a<<8)+b) >= '$REQUIRED_VERSION'.split('.').map(Number).reduce((a,b,i)=>(a<<8)+b) ? 0 : 1)"; then
    echo "❌ Node.js version $NODE_VERSION is too old. Please upgrade to Node.js 16.0.0 or higher."
    exit 1
fi

echo "✅ Node.js version $NODE_VERSION detected"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ npm detected"
echo ""

# Install Google CLI globally
echo "📦 Installing Google CLI globally..."
if npm install -g google-cli; then
    echo "✅ Google CLI installed successfully!"
else
    echo "❌ Failed to install Google CLI from npm."
    echo "   You can try installing from source instead:"
    echo "   git clone https://github.com/Sharma-IT/google-cli.git"
    echo "   cd google-cli"
    echo "   npm install"
    echo "   npm run build"
    echo "   npm link"
    exit 1
fi

echo ""
echo "🎉 Installation Complete!"
echo ""
echo "Next steps:"
echo "1. Set up your Google API credentials:"
echo "   export GOOGLE_API_KEY=\"your-api-key-here\""
echo "   export GOOGLE_SEARCH_ENGINE_ID=\"your-search-engine-id-here\""
echo ""
echo "2. For detailed setup instructions, see:"
echo "   https://github.com/Sharma-IT/google-cli/blob/main/docs/API_SETUP.md"
echo ""
echo "3. Test your installation:"
echo "   google-cli --help"
echo "   google-cli \"test search\""
echo ""
echo "Happy searching! 🔍"
